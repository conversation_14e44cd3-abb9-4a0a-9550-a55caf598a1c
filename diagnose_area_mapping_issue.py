#!/usr/bin/env python3
"""
Diagnose the area mapping issue between orders and delivery personnel
"""

import sys
import os

# Add the project root to the Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def check_area_mapping():
    """Check the area mapping system"""
    print("🗺️ DIAGNOSING AREA MAPPING ISSUE")
    print("=" * 50)
    
    try:
        from src.firebase_db import get_data
        from src.data_storage import get_all_areas, get_area_by_id
        from src.config import restaurants as config_restaurants
        
        # 1. Check Firebase areas
        print("1. Firebase Areas Data:")
        areas_data = get_data("areas") or {}
        firebase_areas = areas_data.get("areas", [])
        
        if firebase_areas:
            print(f"   📊 Found {len(firebase_areas)} areas in Firebase:")
            for area in firebase_areas:
                print(f"      ID: {area.get('id')} → Name: '{area.get('name')}'")
        else:
            print("   ⚠️ No areas found in Firebase")
        
        # 2. Check static config areas
        print(f"\n2. Static Config Areas:")
        print(f"   📊 Found {len(config_restaurants)} areas in config:")
        for i, area_name in enumerate(config_restaurants.keys(), 1):
            print(f"      ID: {i} → Name: '{area_name}'")
        
        # 3. Check delivery personnel service areas
        print(f"\n3. Delivery Personnel Service Areas:")
        personnel_data = get_data("delivery_personnel") or {}
        
        if personnel_data:
            print(f"   📊 Found {len(personnel_data)} delivery personnel:")
            for pid, pdata in personnel_data.items():
                name = pdata.get('name', 'Unknown')
                service_areas = pdata.get('service_areas', [])
                print(f"      {name} ({pid}): {service_areas}")
        else:
            print("   ⚠️ No delivery personnel found")
        
        # 4. Test area lookup functions
        print(f"\n4. Testing Area Lookup Functions:")
        
        # Test get_area_by_id for different ID formats
        test_ids = ['1', '2', '3', 1, 2, 3]
        for test_id in test_ids:
            area = get_area_by_id(test_id)
            print(f"   get_area_by_id({repr(test_id)}): {area}")
        
        return True
        
    except Exception as e:
        print(f"❌ Error checking area mapping: {e}")
        import traceback
        traceback.print_exc()
        return False

def check_order_area_assignment():
    """Check how orders get area assignments"""
    print("\n📋 CHECKING ORDER AREA ASSIGNMENT")
    print("=" * 50)
    
    try:
        from src.firebase_db import get_data
        
        # Check recent orders to see what area IDs they use
        orders_data = get_data("orders") or {}
        
        print(f"📊 Found {len(orders_data)} orders in Firebase")
        
        # Look at the most recent orders
        recent_orders = list(orders_data.items())[-5:] if orders_data else []
        
        for order_id, order_data in recent_orders:
            print(f"\n   Order: {order_id}")
            
            # Check different area ID fields
            area_fields = ['area_id', 'restaurant_area_id', 'area', 'delivery_area_id']
            for field in area_fields:
                value = order_data.get(field)
                if value is not None:
                    print(f"      {field}: {repr(value)} (type: {type(value)})")
        
        return True
        
    except Exception as e:
        print(f"❌ Error checking order area assignment: {e}")
        return False

def test_personnel_area_matching():
    """Test the personnel area matching logic"""
    print("\n🎯 TESTING PERSONNEL AREA MATCHING")
    print("=" * 50)
    
    try:
        from src.data_models import DeliveryPersonnel
        from src.firebase_db import get_data
        
        # Get delivery personnel
        personnel_data = get_data("delivery_personnel") or {}
        
        # Test area matching for each personnel
        test_area_ids = ['1', '2', '3', 1, 2, 3, 'area_bole', 'area_4kilo', 'area_6kilo']
        
        for pid, pdata in personnel_data.items():
            name = pdata.get('name', 'Unknown')
            print(f"\n   Testing {name} ({pid}):")
            
            personnel = DeliveryPersonnel.from_dict(pdata)
            print(f"      Service areas: {personnel.service_areas}")
            
            for test_area in test_area_ids:
                can_serve = personnel.can_serve_area(str(test_area))
                print(f"      Can serve area '{test_area}': {can_serve}")
        
        return True
        
    except Exception as e:
        print(f"❌ Error testing personnel area matching: {e}")
        import traceback
        traceback.print_exc()
        return False

def find_correct_area_mapping():
    """Find the correct area mapping that should be used"""
    print("\n🔍 FINDING CORRECT AREA MAPPING")
    print("=" * 50)
    
    try:
        from src.firebase_db import get_data
        from src.data_storage import get_all_areas, initialize_areas_from_config
        from src.config import restaurants as config_restaurants
        
        # Initialize areas from config if needed
        initialize_areas_from_config()
        
        # Get current areas
        all_areas = get_all_areas()
        print(f"📊 Current areas system:")
        
        for area in all_areas:
            area_id = area.get('id')
            area_name = area.get('name')
            print(f"   ID: {area_id} → Name: '{area_name}'")
        
        # Map config restaurants to area IDs
        print(f"\n🗺️ Recommended area mapping:")
        area_mapping = {}
        
        for i, (area_name, restaurants) in enumerate(config_restaurants.items(), 1):
            area_mapping[area_name] = str(i)  # Use string IDs for consistency
            print(f"   '{area_name}' → Area ID: '{i}'")
            
            # Show some restaurants in this area
            restaurant_names = [r['name'] for r in restaurants.values()][:3]
            print(f"      Restaurants: {', '.join(restaurant_names)}{'...' if len(restaurants) > 3 else ''}")
        
        print(f"\n💡 SOLUTION:")
        print(f"   Orders use numeric area IDs: '1', '2', '3', etc.")
        print(f"   Personnel should use the same numeric area IDs")
        print(f"   Current personnel use: ['area_bole', 'area_4kilo', 'area_6kilo']")
        print(f"   Should be changed to: ['1', '2', '3', '4', '5'] (or specific area IDs)")
        
        return area_mapping
        
    except Exception as e:
        print(f"❌ Error finding correct area mapping: {e}")
        return {}

def main():
    """Run complete area mapping diagnosis"""
    print("🚀 AREA MAPPING DIAGNOSIS")
    print("=" * 60)
    
    tests = [
        check_area_mapping,
        check_order_area_assignment,
        test_personnel_area_matching,
        find_correct_area_mapping
    ]
    
    results = []
    for test_func in tests:
        try:
            result = test_func()
            results.append(result)
            print(f"\n{'✅ COMPLETED' if result else '❌ FAILED'}: {test_func.__name__}")
        except Exception as e:
            print(f"\n❌ ERROR in {test_func.__name__}: {e}")
            results.append(False)
    
    print("\n" + "=" * 60)
    print("📊 DIAGNOSIS SUMMARY")
    print("=" * 60)
    
    passed = sum(results)
    total = len(results)
    
    print(f"✅ Completed: {passed}/{total}")
    print(f"❌ Failed: {total - passed}/{total}")
    
    print(f"\n🎯 KEY FINDINGS:")
    print(f"   • Orders use numeric area IDs (e.g., '2')")
    print(f"   • Personnel use named area IDs (e.g., 'area_bole')")
    print(f"   • This mismatch prevents order broadcasts")
    print(f"   • Solution: Update personnel service areas to use numeric IDs")
    
    return True

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
