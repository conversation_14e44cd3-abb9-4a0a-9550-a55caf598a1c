#!/usr/bin/env python3
"""
Reload delivery personnel data and test availability
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def reload_and_test():
    """Reload delivery personnel data and test availability"""
    print("=== RELOADING DELIVERY PERSONNEL DATA ===")
    
    # 1. Force reload all data using load_user_data()
    print("1. Loading all user data...")
    from src.data_storage import load_user_data
    load_user_data()
    print("✅ Data loaded")
    
    # 2. Check data_models after loading
    print("\n2. Checking data_models after loading...")
    from src.data_models import delivery_personnel, delivery_personnel_availability, delivery_personnel_capacity
    
    print(f"Personnel count: {len(delivery_personnel)}")
    print(f"Availability count: {len(delivery_personnel_availability)}")
    print(f"Capacity count: {len(delivery_personnel_capacity)}")
    
    target_id = "dp_31fe5be0"
    if target_id in delivery_personnel:
        print(f"✅ {target_id} found in delivery_personnel")
        personnel_data = delivery_personnel[target_id]
        print(f"  Status: {personnel_data.get('status')}")
        print(f"  Verified: {personnel_data.get('is_verified')}")
        print(f"  Service Areas: {personnel_data.get('service_areas')}")
    else:
        print(f"❌ {target_id} NOT found in delivery_personnel")
        print("Available personnel IDs:")
        for pid in delivery_personnel.keys():
            print(f"  {pid}")
    
    if target_id in delivery_personnel_availability:
        print(f"✅ {target_id} availability: {delivery_personnel_availability[target_id]}")
    else:
        print(f"❌ {target_id} NOT found in availability")
    
    if target_id in delivery_personnel_capacity:
        print(f"✅ {target_id} capacity: {delivery_personnel_capacity[target_id]}")
    else:
        print(f"❌ {target_id} NOT found in capacity")
    
    # 3. Test find_available_personnel
    print("\n3. Testing find_available_personnel...")
    try:
        from src.utils.delivery_personnel_utils import find_available_personnel
        
        for area_id in ['1', '2', '3', '4']:
            available_personnel = find_available_personnel(area_id)
            print(f"Area {area_id}: {len(available_personnel)} personnel - {available_personnel}")
            
            if target_id in available_personnel:
                print(f"  ✅ {target_id} is available for area {area_id}")
            else:
                print(f"  ❌ {target_id} is NOT available for area {area_id}")
        
        # Test with all available personnel
        all_available = find_available_personnel("1")  # Test area 1
        if all_available:
            print(f"\n✅ Found {len(all_available)} available personnel total")
            return True
        else:
            print(f"\n❌ No available personnel found")
            return False
            
    except Exception as e:
        print(f"❌ Error testing find_available_personnel: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_delivery_personnel_object():
    """Test DeliveryPersonnel object creation and is_available()"""
    print("\n4. Testing DeliveryPersonnel object...")
    
    try:
        from src.data_models import delivery_personnel, DeliveryPersonnel
        
        target_id = "dp_31fe5be0"
        if target_id not in delivery_personnel:
            print(f"❌ {target_id} not found in delivery_personnel")
            return False
        
        personnel_data = delivery_personnel[target_id]
        personnel = DeliveryPersonnel.from_dict(personnel_data)
        
        print(f"Personnel object created:")
        print(f"  ID: {personnel.personnel_id}")
        print(f"  Name: {personnel.name}")
        print(f"  Status: {personnel.status}")
        print(f"  Verified: {personnel.is_verified}")
        print(f"  Current Capacity: {personnel.current_capacity}")
        print(f"  Max Capacity: {personnel.max_capacity}")
        print(f"  Service Areas: {personnel.service_areas}")
        
        # Test is_available()
        is_available = personnel.is_available()
        print(f"\nis_available() result: {is_available}")
        
        # Check individual conditions
        status_ok = personnel.status == "available"
        verified_ok = personnel.is_verified
        capacity_ok = personnel.current_capacity < personnel.max_capacity
        
        print(f"  status == 'available': {status_ok}")
        print(f"  is_verified: {verified_ok}")
        print(f"  current_capacity < max_capacity: {capacity_ok}")
        
        # Test can_serve_area
        print(f"\ncan_serve_area() tests:")
        for area in ['1', '2', '3', '4', '5']:
            can_serve = personnel.can_serve_area(area)
            print(f"  Area {area}: {can_serve}")
        
        return is_available
        
    except Exception as e:
        print(f"❌ Error testing DeliveryPersonnel object: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("🔄 RELOADING AND TESTING DELIVERY PERSONNEL")
    print("=" * 60)
    
    # Step 1: Reload data
    reload_success = reload_and_test()
    
    if reload_success:
        # Step 2: Test personnel object
        object_success = test_delivery_personnel_object()
        
        if object_success:
            print(f"\n🎉 SUCCESS: Personnel 1133538088 is available and should receive broadcasts!")
        else:
            print(f"\n⚠️  Personnel object test failed")
    else:
        print(f"\n❌ Data reload test failed")
