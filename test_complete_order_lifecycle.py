#!/usr/bin/env python3
"""
Test script for the complete order lifecycle with delivery completion and customer confirmation.
Tests the enhanced workflow: Order → Payment → Delivery Broadcast → Acceptance → Completion → Customer Confirmation
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_delivery_bot_complete_order_handler():
    """Test delivery bot complete order callback handler"""
    print("\n🚚 Testing Delivery Bot Complete Order Handler...")
    print("=" * 60)
    
    try:
        # Test import of delivery bot callback handler
        from src.bots.delivery_bot import delivery_bot
        print("✅ Delivery bot instance accessible")
        
        # Check if complete order callback is registered
        handlers = delivery_bot._message_handlers
        callback_handlers = delivery_bot._callback_query_handlers
        
        complete_order_handler_found = False
        for handler in callback_handlers:
            if hasattr(handler, 'func') and handler.func:
                # Test with sample callback data
                class MockCall:
                    def __init__(self, data):
                        self.data = data
                
                test_call = MockCall("complete_order_TEST123")
                try:
                    if handler.func(test_call):
                        complete_order_handler_found = True
                        break
                except:
                    pass
        
        if complete_order_handler_found:
            print("✅ Complete order callback handler is properly registered")
        else:
            print("⚠️  Complete order callback handler registration unclear")
        
        return True
        
    except ImportError as e:
        print(f"❌ Failed to import delivery bot: {e}")
        return False
    except Exception as e:
        print(f"❌ Error testing delivery bot handler: {e}")
        return False

def test_order_tracking_notifications():
    """Test order tracking bot notification functions"""
    print("\n📊 Testing Order Tracking Bot Notifications...")
    print("=" * 60)
    
    try:
        # Test import of notification functions
        from src.bots.order_track_bot import (
            notify_delivery_accepted,
            notify_delivery_completed,
            notify_customer_confirmed,
            send_customer_confirmation_request
        )
        print("✅ All order tracking notification functions accessible")
        
        # Test notification function signatures
        import inspect
        
        # Check notify_delivery_completed signature
        sig = inspect.signature(notify_delivery_completed)
        params = list(sig.parameters.keys())
        if 'order_number' in params and 'personnel_name' in params:
            print("✅ notify_delivery_completed has correct signature")
        else:
            print(f"⚠️  notify_delivery_completed signature: {params}")
        
        # Check send_customer_confirmation_request signature
        sig = inspect.signature(send_customer_confirmation_request)
        params = list(sig.parameters.keys())
        if 'order_number' in params:
            print("✅ send_customer_confirmation_request has correct signature")
        else:
            print(f"⚠️  send_customer_confirmation_request signature: {params}")
        
        return True
        
    except ImportError as e:
        print(f"❌ Failed to import order tracking functions: {e}")
        return False
    except Exception as e:
        print(f"❌ Error testing order tracking notifications: {e}")
        return False

def test_customer_confirmation_workflow():
    """Test customer confirmation workflow"""
    print("\n👤 Testing Customer Confirmation Workflow...")
    print("=" * 60)
    
    try:
        # Test import of customer confirmation handler
        from src.handlers.order_handlers import handle_delivery_confirmation
        print("✅ Customer confirmation handler accessible")
        
        # Test import of delivery personnel utility
        from src.utils.delivery_personnel_utils import get_delivery_personnel_by_id
        print("✅ Delivery personnel utility function accessible")
        
        # Test function signature
        import inspect
        sig = inspect.signature(get_delivery_personnel_by_id)
        params = list(sig.parameters.keys())
        if 'personnel_id' in params:
            print("✅ get_delivery_personnel_by_id has correct signature")
        else:
            print(f"⚠️  get_delivery_personnel_by_id signature: {params}")
        
        return True
        
    except ImportError as e:
        print(f"❌ Failed to import customer confirmation components: {e}")
        return False
    except Exception as e:
        print(f"❌ Error testing customer confirmation workflow: {e}")
        return False

def test_firebase_order_status_structure():
    """Test Firebase order status structure for the new workflow"""
    print("\n🔥 Testing Firebase Order Status Structure...")
    print("=" * 60)
    
    try:
        from src.firebase_db import get_data, set_data, delete_data
        
        # Test confirmed_orders collection access
        confirmed_orders = get_data("confirmed_orders")
        print("✅ Confirmed orders collection accessible")
        
        # Test order status structure
        test_order = {
            "order_number": "TEST_LIFECYCLE_001",
            "user_id": "test_user",
            "status": "CONFIRMED",
            "delivery_status": "pending_assignment",
            "confirmed_at": "2025-07-01 12:00:00",
            "restaurant_id": "1",
            "subtotal": 150,
            "phone_number": "+251963630623",
            "delivery_location": "Test Location"
        }
        
        # Test write
        if set_data("confirmed_orders/TEST_LIFECYCLE_001", test_order):
            print("✅ Successfully wrote test order with new status structure")
            
            # Test status updates
            test_order["delivery_status"] = "assigned"
            test_order["assigned_to"] = "dp_test123"
            test_order["assigned_at"] = "2025-07-01 12:05:00"
            
            if set_data("confirmed_orders/TEST_LIFECYCLE_001", test_order):
                print("✅ Successfully updated order to 'assigned' status")
                
                # Test completion status
                test_order["delivery_status"] = "completed"
                test_order["completed_at"] = "2025-07-01 12:30:00"
                test_order["completed_by"] = "dp_test123"
                
                if set_data("confirmed_orders/TEST_LIFECYCLE_001", test_order):
                    print("✅ Successfully updated order to 'completed' status")
                    
                    # Test customer confirmation
                    test_order["status"] = "CUSTOMER_CONFIRMED"
                    test_order["delivery_status"] = "customer_confirmed"
                    test_order["customer_confirmed_at"] = "2025-07-01 12:35:00"
                    
                    if set_data("confirmed_orders/TEST_LIFECYCLE_001", test_order):
                        print("✅ Successfully updated order to 'customer_confirmed' status")
                    else:
                        print("❌ Failed to update order to customer confirmed status")
                else:
                    print("❌ Failed to update order to completed status")
            else:
                print("❌ Failed to update order to assigned status")
            
            # Clean up test order
            if delete_data("confirmed_orders/TEST_LIFECYCLE_001"):
                print("✅ Successfully cleaned up test order")
            else:
                print("⚠️  Failed to clean up test order")
                
        else:
            print("❌ Failed to write test order")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ Error testing Firebase order status structure: {e}")
        return False

def test_error_handling():
    """Test error handling for unreachable delivery personnel"""
    print("\n⚠️  Testing Error Handling...")
    print("=" * 60)
    
    try:
        # Test import of enhanced error handling
        from src.handlers.payment_handlers import handle_finance_verification
        print("✅ Enhanced payment handlers accessible")
        
        # Test delivery personnel utilities
        from src.utils.delivery_personnel_utils import (
            find_available_personnel,
            get_delivery_personnel_by_id,
            delivery_personnel
        )
        print("✅ Delivery personnel utilities accessible")
        
        # Test if delivery personnel data structure supports error tracking
        if delivery_personnel:
            sample_personnel = next(iter(delivery_personnel.values()))
            if 'telegram_id' in sample_personnel:
                print("✅ Delivery personnel data includes telegram_id for error tracking")
            else:
                print("⚠️  Delivery personnel data structure may need telegram_id field")
        else:
            print("⚠️  No delivery personnel data available for testing")
        
        return True
        
    except ImportError as e:
        print(f"❌ Failed to import error handling components: {e}")
        return False
    except Exception as e:
        print(f"❌ Error testing error handling: {e}")
        return False

def main():
    """Run all tests for the complete order lifecycle"""
    print("🧪 TESTING COMPLETE ORDER LIFECYCLE IMPLEMENTATION")
    print("=" * 80)
    
    tests = [
        test_delivery_bot_complete_order_handler,
        test_order_tracking_notifications,
        test_customer_confirmation_workflow,
        test_firebase_order_status_structure,
        test_error_handling
    ]
    
    passed = 0
    total = len(tests)
    
    for test in tests:
        try:
            if test():
                passed += 1
        except Exception as e:
            print(f"❌ Test {test.__name__} failed with exception: {e}")
    
    print("\n" + "=" * 80)
    print(f"📊 TEST RESULTS: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 ALL TESTS PASSED! Complete order lifecycle implementation is ready.")
        print("\n📋 NEXT STEPS:")
        print("1. Restart all bots to load the new functionality")
        print("2. Test with a real order to verify the complete workflow")
        print("3. Monitor logs for any issues during real-world testing")
    else:
        print("⚠️  Some tests failed. Please review the implementation before proceeding.")
    
    return passed == total

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
