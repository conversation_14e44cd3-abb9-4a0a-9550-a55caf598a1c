#!/usr/bin/env python3
"""
Test Firebase Connection
Tests if Firebase is causing the management bot to hang
"""

import sys
import os
import time

# Add the project root to the Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_firebase_connection():
    """Test Firebase connection"""
    print("🧪 TESTING FIREBASE CONNECTION")
    print("=" * 40)
    
    try:
        print("1. Testing Firebase import...")
        from src.firebase_db import get_data, set_data
        print("✅ Firebase imported successfully")
        
        print("2. Testing Firebase connection...")
        start_time = time.time()
        test_data = get_data("test_connection")
        end_time = time.time()
        print(f"✅ Firebase connection test completed in {end_time - start_time:.2f} seconds")
        print(f"   Result: {test_data}")
        
        print("3. Testing data models import...")
        start_time = time.time()
        from src.data_models import delivery_personnel, delivery_personnel_availability
        end_time = time.time()
        print(f"✅ Data models imported in {end_time - start_time:.2f} seconds")
        print(f"   Personnel count: {len(delivery_personnel)}")
        print(f"   Availability count: {len(delivery_personnel_availability)}")
        
        print("4. Testing data storage import...")
        start_time = time.time()
        from src.data_storage import load_user_data
        end_time = time.time()
        print(f"✅ Data storage imported in {end_time - start_time:.2f} seconds")
        
        print("5. Testing data loading...")
        start_time = time.time()
        load_user_data()
        end_time = time.time()
        print(f"✅ Data loaded in {end_time - start_time:.2f} seconds")
        
        print("6. Testing management bot import...")
        start_time = time.time()
        from src.bots.management_bot import management_bot
        end_time = time.time()
        print(f"✅ Management bot imported in {end_time - start_time:.2f} seconds")
        
        bot_info = management_bot.get_me()
        print(f"✅ Bot connected: @{bot_info.username}")
        
        return True
        
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = test_firebase_connection()
    if success:
        print("\n🎉 Firebase connection test completed!")
    else:
        print("\n💥 Firebase connection test failed!")
        sys.exit(1)
