#!/usr/bin/env python3
"""
Force refresh data in running bots to ensure they have the latest Firebase data
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

import asyncio
import requests
import json
from src.config import ADMIN_BOT_TOKEN, DELIVERY_BOT_TOKEN, ORDER_TRACK_BOT_TOKEN

def send_refresh_command_to_bot(bot_token, chat_id, command_text):
    """Send a refresh command to a running bot"""
    try:
        url = f"https://api.telegram.org/bot{bot_token}/sendMessage"
        data = {
            "chat_id": chat_id,
            "text": command_text
        }
        
        response = requests.post(url, data=data)
        if response.status_code == 200:
            print(f"✅ Successfully sent refresh command to bot")
            return True
        else:
            print(f"❌ Failed to send command: {response.status_code} - {response.text}")
            return False
            
    except Exception as e:
        print(f"❌ Error sending command to bot: {e}")
        return False

def force_refresh_all_bots():
    """Force refresh data in all running bots"""
    print("🔄 FORCING DATA REFRESH IN RUNNING BOTS")
    print("=" * 60)
    
    # Admin chat ID (authorized user)
    admin_chat_id = "7729984017"
    
    # Commands to refresh data
    refresh_commands = [
        "/refresh_data",
        "/reload_personnel",
        "/refresh_personnel_data"
    ]
    
    bots_to_refresh = [
        ("Admin Bot", ADMIN_BOT_TOKEN),
        ("Delivery Bot", DELIVERY_BOT_TOKEN),
        ("Order Tracking Bot", ORDER_TRACK_BOT_TOKEN)
    ]
    
    success_count = 0
    total_attempts = 0
    
    for bot_name, bot_token in bots_to_refresh:
        print(f"\n📡 Refreshing {bot_name}...")
        
        for command in refresh_commands:
            total_attempts += 1
            print(f"  Sending: {command}")
            
            if send_refresh_command_to_bot(bot_token, admin_chat_id, command):
                success_count += 1
                print(f"  ✅ Command sent successfully")
            else:
                print(f"  ❌ Command failed")
    
    print(f"\n📊 REFRESH SUMMARY:")
    print(f"  Commands sent: {success_count}/{total_attempts}")
    print(f"  Success rate: {(success_count/total_attempts)*100:.1f}%")
    
    if success_count > 0:
        print(f"\n⏳ Waiting 10 seconds for bots to process refresh commands...")
        import time
        time.sleep(10)
        print(f"✅ Refresh commands should be processed")
    
    return success_count > 0

def test_delivery_personnel_after_refresh():
    """Test delivery personnel availability after refresh"""
    print("\n🔍 TESTING DELIVERY PERSONNEL AFTER REFRESH")
    print("=" * 60)
    
    try:
        # Load fresh data
        from src.data_storage import load_user_data
        from src.utils.delivery_personnel_utils import find_available_personnel
        from src.data_models import delivery_personnel, delivery_personnel_availability
        
        print("📥 Loading fresh data...")
        load_user_data()
        
        # Check target personnel
        target_personnel_id = "dp_31fe5be0"
        target_telegram_id = "1133538088"
        
        print(f"\n👤 Checking target personnel {target_personnel_id} ({target_telegram_id}):")
        
        if target_personnel_id in delivery_personnel:
            personnel_data = delivery_personnel[target_personnel_id]
            availability_status = delivery_personnel_availability.get(target_personnel_id)
            
            print(f"  ✅ Found in delivery_personnel")
            print(f"  Status: {personnel_data.get('status')}")
            print(f"  Verified: {personnel_data.get('is_verified')}")
            print(f"  Service Areas: {personnel_data.get('service_areas')}")
            print(f"  Availability: {availability_status}")
        else:
            print(f"  ❌ NOT found in delivery_personnel")
            return False
        
        # Test availability for different areas
        print(f"\n📍 Testing availability for different areas:")
        found_available = False
        
        for area_id in ['1', '2', '3', '4']:
            available_personnel = find_available_personnel(area_id)
            print(f"  Area {area_id}: {len(available_personnel)} personnel - {available_personnel}")
            
            if target_personnel_id in available_personnel:
                print(f"    ✅ {target_personnel_id} is available for area {area_id}")
                found_available = True
            else:
                print(f"    ❌ {target_personnel_id} is NOT available for area {area_id}")
        
        if found_available:
            print(f"\n🎉 SUCCESS: Target personnel is now available for delivery!")
            return True
        else:
            print(f"\n❌ ISSUE: Target personnel is still not available for any area")
            return False
            
    except Exception as e:
        print(f"❌ Error testing delivery personnel: {e}")
        import traceback
        traceback.print_exc()
        return False

def check_bot_startup_status():
    """Check if bots are fully started by looking at logs"""
    print("\n🔍 CHECKING BOT STARTUP STATUS")
    print("=" * 60)
    
    try:
        with open("logs/bot_2025-07-02.log", "r") as f:
            log_content = f.read()
        
        # Look for key startup indicators
        startup_indicators = [
            "All bots and data save thread started successfully",
            "User bot is running...",
            "Admin bot is running...",
            "Finance bot is running...",
            "Maintenance bot is running...",
            "Notification bot is running...",
            "Starting order tracking bot...",
            "Starting delivery bot..."
        ]
        
        found_indicators = []
        for indicator in startup_indicators:
            if indicator in log_content:
                found_indicators.append(indicator)
                print(f"  ✅ {indicator}")
            else:
                print(f"  ❌ {indicator}")
        
        if "All bots and data save thread started successfully" in log_content:
            print(f"\n🎉 ALL BOTS ARE FULLY STARTED!")
            return True
        else:
            print(f"\n⏳ Bots are still starting up...")
            print(f"Found {len(found_indicators)}/{len(startup_indicators)} startup indicators")
            return False
            
    except Exception as e:
        print(f"❌ Error checking bot status: {e}")
        return False

if __name__ == "__main__":
    print("🚀 FORCE REFRESH RUNNING BOTS")
    print("=" * 80)
    
    # 1. Check if bots are fully started
    bots_started = check_bot_startup_status()
    
    if not bots_started:
        print(f"\n⚠️  Bots are not fully started yet. Waiting 30 seconds...")
        import time
        time.sleep(30)
        bots_started = check_bot_startup_status()
    
    if bots_started:
        # 2. Force refresh data in running bots
        refresh_success = force_refresh_all_bots()
        
        # 3. Test delivery personnel availability
        test_success = test_delivery_personnel_after_refresh()
        
        if test_success:
            print(f"\n🎉 SUCCESS: All fixes are working and bots have fresh data!")
            print(f"\n🚀 READY FOR REAL ORDER TEST!")
        else:
            print(f"\n❌ ISSUE: Data refresh may not have worked completely")
    else:
        print(f"\n⚠️  Bots are still not fully started. Manual intervention may be needed.")
        print(f"\n💡 RECOMMENDATION: Wait for bots to fully start, then run this script again.")
