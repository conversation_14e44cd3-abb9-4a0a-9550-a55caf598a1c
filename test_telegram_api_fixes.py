#!/usr/bin/env python3
"""
Test script for Telegram API error fixes in the management bot.
This script verifies that the implemented fixes resolve the MESSAGE_TOO_LONG and
Message Not Modified errors.
"""

import sys
import os

# Add the project root to the Python path
sys.path.insert(0, os.path.abspath('.'))

def test_message_length_validation():
    """Test message length validation functions"""
    try:
        print("🧪 Testing Message Length Validation...")

        from src.bots.management_bot import (
            validate_message_length,
            truncate_message_content
        )

        # Test message length validation
        short_message = "This is a short message"
        long_message = "A" * 5000  # Exceeds 4096 character limit

        assert validate_message_length(short_message) == True, "Short message validation failed"
        assert validate_message_length(long_message) == False, "Long message validation failed"
        print("✅ Message length validation working correctly")

        # Test message truncation
        truncated, was_truncated = truncate_message_content(long_message, max_length=100)
        assert len(truncated) <= 100, "Message truncation failed"
        assert was_truncated == True, "Truncation flag incorrect"
        print("✅ Message truncation working correctly")
        
        # Test pagination
        pages = create_paginated_content(long_message, page_size=1000)
        assert len(pages) > 1, "Pagination failed"
        print("✅ Content pagination working correctly")
        
        # Test personnel list optimization
        test_personnel = [
            {'name': 'John Doe', 'earnings': 100, 'status': 'active'},
            {'name': 'Jane Smith', 'earnings': 200, 'status': 'active'},
            {'name': 'Bob Johnson', 'earnings': 150, 'status': 'busy'},
        ]
        optimized_list, count = optimize_personnel_list(test_personnel, max_items=2)
        assert "Jane Smith" in optimized_list, "Personnel optimization failed"
        assert count == 3, "Personnel count incorrect"
        print("✅ Personnel list optimization working correctly")
        
        return True
        
    except Exception as e:
        print(f"❌ Message length validation test failed: {e}")
        return False

def test_refresh_button_logic():
    """Test refresh button logic and timestamp functions"""
    try:
        print("\n🧪 Testing Refresh Button Logic...")
        
        from src.bots.management_bot import (
            add_refresh_timestamp,
            safe_calculate_percentage,
            safe_get_numeric_value
        )
        
        # Test timestamp addition
        original_text = "Test analytics message"
        timestamped_text = add_refresh_timestamp(original_text)
        assert "Last Updated:" in timestamped_text, "Timestamp addition failed"
        print("✅ Timestamp addition working correctly")
        
        # Test safe calculations
        percentage = safe_calculate_percentage(50, 100, 0)
        assert percentage == 50.0, "Safe percentage calculation failed"
        
        percentage_zero = safe_calculate_percentage(50, 0, 0)  # Division by zero
        assert percentage_zero == 0, "Safe percentage calculation (division by zero) failed"
        print("✅ Safe calculations working correctly")
        
        # Test safe numeric value extraction
        test_data = {"valid_number": "123.45", "invalid_number": "abc", "missing_key": None}
        
        valid_value = safe_get_numeric_value(test_data, "valid_number", 0)
        assert valid_value == 123.45, "Safe numeric extraction failed"
        
        invalid_value = safe_get_numeric_value(test_data, "invalid_number", 0)
        assert invalid_value == 0, "Safe numeric extraction (invalid) failed"
        
        missing_value = safe_get_numeric_value(test_data, "missing_key", 0)
        assert missing_value == 0, "Safe numeric extraction (missing) failed"
        print("✅ Safe numeric value extraction working correctly")
        
        return True
        
    except Exception as e:
        print(f"❌ Refresh button logic test failed: {e}")
        return False

def test_error_recovery():
    """Test error recovery and fallback mechanisms"""
    try:
        print("\n🧪 Testing Error Recovery...")
        
        from src.bots.management_bot import (
            create_emergency_fallback_message,
            validate_analytics_data
        )
        
        # Test emergency fallback message creation
        long_analytics_text = """
📅 **Daily Analytics - 2025-01-15**

**📊 Order Summary:**
• Total Orders: 25
• Completed: 20
• In Progress: 5
• Success Rate: 80.0%

**💰 Revenue Analytics:**
• Food Revenue: 1500.00 birr
• Delivery Fees: 250.00 birr
• Total Revenue: 1750.00 birr
• Average Order: 87.50 birr

**📈 Profit Analytics:**
• Total Profit: 250.00 birr
• Profit Margin: 14.3%
• Personnel Earnings: 125.00 birr
• Company Profit: 125.00 birr

**👥 Personnel Status:**
• Active: 5
• Busy: 3
• Total: 8
        """
        
        emergency_message = create_emergency_fallback_message(long_analytics_text)
        assert len(emergency_message) < 1000, "Emergency fallback too long"
        assert "Daily Analytics" in emergency_message, "Emergency fallback missing key content"
        print("✅ Emergency fallback message creation working correctly")
        
        # Test data validation
        valid_data = {"order1": {"subtotal": 100}, "order2": {"subtotal": 200}}
        invalid_data = {"order1": "invalid", "order2": {"subtotal": 200}}
        
        validated_valid = validate_analytics_data(valid_data, "test_orders")
        assert len(validated_valid) == 2, "Valid data validation failed"
        
        validated_invalid = validate_analytics_data(invalid_data, "test_orders")
        assert len(validated_invalid) == 1, "Invalid data validation failed"
        print("✅ Data validation working correctly")
        
        return True
        
    except Exception as e:
        print(f"❌ Error recovery test failed: {e}")
        return False

def test_optimized_display():
    """Test optimized analytics display functions"""
    try:
        print("\n🧪 Testing Optimized Display...")
        
        # Test that optimized functions can be imported
        from src.bots.management_bot import (
            show_daily_analytics,
            show_weekly_analytics,
            show_payroll_analytics,
            escape_markdown
        )
        
        print("✅ Optimized analytics functions imported successfully")
        
        # Test markdown escaping (critical for preventing parse errors)
        test_values = [
            ("25.50", "25\\.50"),
            ("Success Rate: 95.5%", "Success Rate: 95\\.5%"),
            ("John_Doe", "John\\_Doe"),
            ("Order #123", "Order \\#123")
        ]
        
        for original, expected in test_values:
            escaped = escape_markdown(original)
            assert escaped == expected, f"Markdown escaping failed for '{original}'"
        
        print("✅ Markdown escaping working correctly")
        
        return True
        
    except Exception as e:
        print(f"❌ Optimized display test failed: {e}")
        return False

def main():
    """Run all tests for Telegram API fixes"""
    print("🚀 TESTING TELEGRAM API ERROR FIXES")
    print("=" * 60)
    
    tests = [
        ("Message Length Validation", test_message_length_validation),
        ("Refresh Button Logic", test_refresh_button_logic),
        ("Error Recovery", test_error_recovery),
        ("Optimized Display", test_optimized_display)
    ]
    
    passed = 0
    failed = 0
    
    for test_name, test_func in tests:
        try:
            if test_func():
                passed += 1
                print(f"✅ {test_name} - PASSED")
            else:
                failed += 1
                print(f"❌ {test_name} - FAILED")
        except Exception as e:
            failed += 1
            print(f"❌ {test_name} - ERROR: {e}")
    
    print("\n" + "=" * 60)
    print(f"📊 TEST RESULTS")
    print(f"✅ Passed: {passed}")
    print(f"❌ Failed: {failed}")
    print(f"📈 Success Rate: {(passed/(passed+failed)*100):.1f}%")
    
    if failed == 0:
        print("\n🎉 ALL TESTS PASSED!")
        print("\n📋 TELEGRAM API FIXES VERIFIED:")
        print("• ✅ Message length validation and truncation")
        print("• ✅ Refresh button logic with timestamps")
        print("• ✅ Error recovery and fallback mechanisms")
        print("• ✅ Optimized display with concise formatting")
        print("• ✅ Safe message editing with multiple fallbacks")
        print("• ✅ Emergency fallback for critical errors")
        print("\n🚀 The management bot should now handle Telegram API errors gracefully!")
    else:
        print(f"\n⚠️ {failed} test(s) failed. Please review the errors above.")
    
    return failed == 0

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
