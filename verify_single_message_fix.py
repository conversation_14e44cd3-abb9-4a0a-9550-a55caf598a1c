#!/usr/bin/env python3
"""
Simple verification script to check if the single message system is properly implemented.
"""

import os
import re

def check_file_content(file_path, expected_patterns, description):
    """Check if file contains expected patterns"""
    print(f"\n📁 Checking {description}: {file_path}")
    
    if not os.path.exists(file_path):
        print(f"❌ File not found: {file_path}")
        return False
    
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        all_found = True
        for pattern, desc in expected_patterns:
            if re.search(pattern, content, re.MULTILINE | re.DOTALL):
                print(f"  ✅ {desc}")
            else:
                print(f"  ❌ {desc}")
                all_found = False
        
        return all_found
        
    except Exception as e:
        print(f"❌ Error reading file: {e}")
        return False

def main():
    """Main verification function"""
    print("🔍 Verifying Single Message Update System Implementation")
    print("=" * 60)
    
    # Check order_track_bot.py
    order_track_patterns = [
        (r'def send_order_status_update.*replace_previous: bool = True', 
         'Default parameter changed to True'),
        (r'delivery_personnel_info.*personnel_name.*personnel_phone', 
         'Delivery personnel info included in messages'),
        (r'Order Tracking.*order_number', 
         'Updated message format'),
        (r'replace_previous=True.*Update existing message', 
         'notify_delivery_assignment uses replace_previous=True'),
        (r'notify_delivery_accepted.*replace_previous=True', 
         'notify_delivery_accepted uses replace_previous=True'),
        (r'notify_delivery_completed.*replace_previous=True', 
         'notify_delivery_completed uses replace_previous=True'),
        (r'notify_customer_confirmed.*replace_previous=True', 
         'notify_customer_confirmed uses replace_previous=True')
    ]
    
    order_track_ok = check_file_content(
        'src/bots/order_track_bot.py', 
        order_track_patterns,
        'Order Tracking Bot'
    )
    
    # Check payment_handlers.py
    payment_patterns = [
        (r'Payment Approved.*Broadcasting to delivery personnel', 
         'Updated initial message text'),
        (r'replace_previous=False.*Initial message', 
         'Initial message uses replace_previous=False'),
        (r'initial order tracking notification', 
         'Updated comment for initial notification')
    ]
    
    payment_ok = check_file_content(
        'src/handlers/payment_handlers.py', 
        payment_patterns,
        'Payment Handlers'
    )
    
    # Summary
    print("\n📊 Verification Summary:")
    print("=" * 30)
    
    if order_track_ok:
        print("✅ Order Tracking Bot: All changes implemented correctly")
    else:
        print("❌ Order Tracking Bot: Some changes missing or incorrect")
    
    if payment_ok:
        print("✅ Payment Handlers: All changes implemented correctly")
    else:
        print("❌ Payment Handlers: Some changes missing or incorrect")
    
    if order_track_ok and payment_ok:
        print("\n🎉 Single Message Update System Successfully Implemented!")
        print("\nExpected Behavior:")
        print("1. Payment approval → Initial tracking message sent")
        print("2. Delivery assignment → Same message updated with personnel info")
        print("3. Delivery acceptance → Same message updated with acceptance status")
        print("4. Delivery completion → Same message updated with completion status")
        print("5. Customer confirmation → Same message updated with final status")
        print("\n📱 Each order will have only ONE tracking message that evolves!")
        return True
    else:
        print("\n❌ Implementation incomplete. Please review the changes.")
        return False

if __name__ == "__main__":
    success = main()
    exit(0 if success else 1)
