#!/usr/bin/env python3
"""
Test script to verify the delivery bot fix for user 7729984017.
"""

import sys
import os

# Add the src directory to the path
sys.path.append(os.path.join(os.path.dirname(__file__), 'src'))

def test_delivery_bot_personnel_lookup():
    """Test the delivery bot's personnel lookup function"""
    print("🧪 Testing Delivery Bot Personnel Lookup Fix")
    print("=" * 50)
    
    try:
        # Import the delivery bot's personnel lookup function
        from src.bots.delivery_bot import get_personnel_by_telegram_id
        
        # Test with user 7729984017
        print("🔍 Looking up user 7729984017...")
        personnel = get_personnel_by_telegram_id(7729984017)
        
        if personnel:
            print("✅ SUCCESS: User 7729984017 found!")
            print(f"   Personnel ID: {personnel.personnel_id}")
            print(f"   Name: {personnel.name}")
            print(f"   Phone: {personnel.phone_number}")
            print(f"   Telegram ID: {personnel.telegram_id}")
            print(f"   Verified: {getattr(personnel, 'is_verified', 'Unknown')}")
            print(f"   Status: {getattr(personnel, 'status', 'Unknown')}")
            print(f"   Service Areas: {personnel.service_areas}")
            return True
        else:
            print("❌ FAILED: User 7729984017 NOT found")
            return False
            
    except Exception as e:
        print(f"❌ ERROR: {e}")
        return False

def test_firebase_direct_access():
    """Test direct Firebase access to delivery personnel"""
    print("\n🧪 Testing Direct Firebase Access")
    print("=" * 50)
    
    try:
        from src.firebase_db import get_data
        
        personnel_data = get_data("delivery_personnel")
        
        if personnel_data:
            print(f"✅ Found {len(personnel_data)} personnel records in Firebase")
            
            # Look for user 7729984017
            for pid, pdata in personnel_data.items():
                if pdata.get('telegram_id') == '7729984017':
                    print("✅ User 7729984017 found in Firebase:")
                    print(f"   Personnel ID: {pdata.get('personnel_id')}")
                    print(f"   Name: {pdata.get('name')}")
                    print(f"   Verified: {pdata.get('is_verified')}")
                    print(f"   Status: {pdata.get('status')}")
                    return True
            
            print("❌ User 7729984017 NOT found in Firebase personnel data")
            return False
        else:
            print("❌ No personnel data found in Firebase")
            return False
            
    except Exception as e:
        print(f"❌ ERROR: {e}")
        return False

def test_bot_authorization():
    """Test bot authorization configuration"""
    print("\n🧪 Testing Bot Authorization Configuration")
    print("=" * 50)
    
    try:
        from src.config import DELIVERY_BOT_AUTHORIZED_IDS
        
        if 7729984017 in DELIVERY_BOT_AUTHORIZED_IDS:
            print("✅ User 7729984017 is authorized for delivery bot")
            return True
        else:
            print("❌ User 7729984017 is NOT authorized for delivery bot")
            return False
            
    except Exception as e:
        print(f"❌ ERROR: {e}")
        return False

if __name__ == "__main__":
    print("🔧 DELIVERY BOT FIX VERIFICATION")
    print("=" * 60)
    
    # Run all tests
    test1_passed = test_firebase_direct_access()
    test2_passed = test_bot_authorization()
    test3_passed = test_delivery_bot_personnel_lookup()
    
    print("\n" + "=" * 60)
    print("📋 TEST RESULTS SUMMARY")
    print("=" * 60)
    print(f"Firebase Access: {'✅ PASS' if test1_passed else '❌ FAIL'}")
    print(f"Bot Authorization: {'✅ PASS' if test2_passed else '❌ FAIL'}")
    print(f"Personnel Lookup: {'✅ PASS' if test3_passed else '❌ FAIL'}")
    
    if all([test1_passed, test2_passed, test3_passed]):
        print("\n🎉 ALL TESTS PASSED!")
        print("✅ Delivery bot should now work for user 7729984017")
        print("\n📱 Try sending /start to the delivery bot now!")
    else:
        print("\n❌ SOME TESTS FAILED!")
        print("🔧 Additional fixes may be needed")
        
    print("\n" + "=" * 60)
