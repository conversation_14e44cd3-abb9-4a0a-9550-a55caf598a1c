#!/usr/bin/env python3
"""
Comprehensive verification script to ensure all Wiz-Aroma management bot data
is stored in and accessed exclusively from Firebase Firestore with no local fallbacks.
"""

import sys
import os
import inspect
import ast
import re
from typing import Dict, List, Set, Any

# Add the project root to the Python path
sys.path.insert(0, os.path.abspath('.'))

def analyze_firebase_collections():
    """Analyze all Firebase collections used in the system"""
    print("🔥 Analyzing Firebase Collections...")
    
    try:
        from src.firebase_db import get_data
        
        # Expected Firestore collections for Wiz-Aroma system
        expected_collections = [
            "delivery_personnel",
            "delivery_personnel_availability", 
            "delivery_personnel_capacity",
            "delivery_personnel_zones",
            "delivery_personnel_performance",
            "delivery_personnel_earnings",
            "delivery_personnel_assignments",
            "confirmed_orders",
            "completed_orders",
            "order_broadcast_messages",
            "delivery_personnel_capacity_tracking",
            "system_health"
        ]
        
        print(f"  📊 Checking {len(expected_collections)} expected collections...")
        
        collection_status = {}
        for collection in expected_collections:
            try:
                data = get_data(collection)
                if data is not None:
                    count = len(data) if isinstance(data, dict) else 0
                    collection_status[collection] = {"exists": True, "count": count}
                    print(f"    ✅ {collection}: {count} records")
                else:
                    collection_status[collection] = {"exists": False, "count": 0}
                    print(f"    📝 {collection}: Empty/New collection")
            except Exception as e:
                collection_status[collection] = {"exists": False, "error": str(e)}
                print(f"    ❌ {collection}: Error - {e}")
        
        return collection_status
        
    except Exception as e:
        print(f"  ❌ Error analyzing collections: {e}")
        return {}

def check_firebase_operations_usage():
    """Check that all management bot functions use Firebase operations"""
    print("\n🔍 Checking Firebase Operations Usage...")
    
    try:
        # Files to analyze for Firebase usage
        files_to_check = [
            "src/bots/management_bot.py",
            "src/utils/delivery_personnel_utils.py",
            "src/utils/earnings_utils.py",
            "src/handlers/payment_handlers.py",
            "src/bots/delivery_bot.py"
        ]
        
        firebase_functions = ["get_data", "set_data", "update_data", "delete_data", "push_data"]
        local_storage_patterns = [
            r"\.json",
            r"pickle",
            r"shelve",
            r"sqlite",
            r"csv",
            r"open\s*\(",
            r"with\s+open",
            r"file\s*=",
            r"\.txt",
            r"\.dat"
        ]
        
        results = {}
        
        for file_path in files_to_check:
            if os.path.exists(file_path):
                print(f"  📄 Analyzing {file_path}...")
                
                with open(file_path, 'r', encoding='utf-8') as f:
                    content = f.read()
                
                # Count Firebase operations
                firebase_usage = {}
                for func in firebase_functions:
                    count = len(re.findall(rf'\b{func}\s*\(', content))
                    firebase_usage[func] = count
                
                # Check for local storage patterns
                local_storage_found = []
                for pattern in local_storage_patterns:
                    matches = re.findall(pattern, content, re.IGNORECASE)
                    if matches:
                        local_storage_found.extend(matches)
                
                results[file_path] = {
                    "firebase_usage": firebase_usage,
                    "local_storage_patterns": local_storage_found,
                    "total_firebase_calls": sum(firebase_usage.values())
                }
                
                print(f"    🔥 Firebase operations: {sum(firebase_usage.values())} total")
                for func, count in firebase_usage.items():
                    if count > 0:
                        print(f"      - {func}: {count} calls")
                
                if local_storage_found:
                    print(f"    ⚠️  Local storage patterns found: {len(local_storage_found)}")
                    for pattern in set(local_storage_found):
                        print(f"      - {pattern}")
                else:
                    print(f"    ✅ No local storage patterns detected")
            else:
                print(f"  ❌ File not found: {file_path}")
        
        return results
        
    except Exception as e:
        print(f"  ❌ Error checking Firebase operations: {e}")
        return {}

def verify_data_models_firebase_sync():
    """Verify that data models are synchronized with Firebase"""
    print("\n🔄 Verifying Data Models Firebase Synchronization...")
    
    try:
        from src.data_models import (
            delivery_personnel,
            delivery_personnel_availability,
            delivery_personnel_capacity,
            delivery_personnel_assignments
        )
        from src.firebase_db import get_data
        
        # Check if data models are populated from Firebase
        models_to_check = {
            "delivery_personnel": delivery_personnel,
            "delivery_personnel_availability": delivery_personnel_availability,
            "delivery_personnel_capacity": delivery_personnel_capacity,
            "delivery_personnel_assignments": delivery_personnel_assignments
        }
        
        sync_results = {}
        
        for model_name, model_data in models_to_check.items():
            print(f"  📊 Checking {model_name}...")
            
            # Get data from Firebase
            firebase_data = get_data(model_name) or {}
            
            # Compare with data model
            model_count = len(model_data) if isinstance(model_data, dict) else 0
            firebase_count = len(firebase_data) if isinstance(firebase_data, dict) else 0
            
            sync_results[model_name] = {
                "model_count": model_count,
                "firebase_count": firebase_count,
                "synchronized": model_count == firebase_count
            }
            
            print(f"    - Model records: {model_count}")
            print(f"    - Firebase records: {firebase_count}")
            print(f"    - Synchronized: {'✅' if model_count == firebase_count else '❌'}")
        
        return sync_results
        
    except Exception as e:
        print(f"  ❌ Error verifying data model sync: {e}")
        return {}

def check_local_file_dependencies():
    """Check for any local file dependencies in the system"""
    print("\n📁 Checking for Local File Dependencies...")
    
    try:
        # Check data_storage.py for local file usage
        data_storage_path = "src/data_storage.py"
        
        if os.path.exists(data_storage_path):
            print(f"  📄 Analyzing {data_storage_path}...")
            
            with open(data_storage_path, 'r', encoding='utf-8') as f:
                content = f.read()
            
            # Check for USE_FIREBASE flag
            use_firebase_match = re.search(r'USE_FIREBASE\s*=\s*(True|False)', content)
            if use_firebase_match:
                use_firebase = use_firebase_match.group(1) == 'True'
                print(f"    🔥 USE_FIREBASE flag: {use_firebase}")
                
                if not use_firebase:
                    print(f"    ⚠️  WARNING: USE_FIREBASE is set to False!")
                else:
                    print(f"    ✅ Firebase is enabled as primary storage")
            
            # Check for file operations
            file_operations = re.findall(r'def\s+(\w*(?:load|save|read|write)\w*)', content)
            if file_operations:
                print(f"    📁 File operations found: {file_operations}")
                
                # Check if these operations have Firebase alternatives
                for op in file_operations:
                    if "firebase" in op.lower() or "firestore" in op.lower():
                        print(f"      ✅ {op}: Firebase-based operation")
                    else:
                        print(f"      ⚠️  {op}: Potentially local file operation")
            
        else:
            print(f"  ❌ data_storage.py not found")
        
        # Check config files for local data paths
        config_files = ["src/config.py", ".env"]
        for config_file in config_files:
            if os.path.exists(config_file):
                print(f"  📄 Checking {config_file} for local data paths...")
                
                with open(config_file, 'r', encoding='utf-8') as f:
                    content = f.read()
                
                # Look for file paths
                file_paths = re.findall(r'["\']([^"\']*\.(?:json|csv|txt|dat|db))["\']', content)
                if file_paths:
                    print(f"    📁 Local file paths found: {file_paths}")
                else:
                    print(f"    ✅ No local file paths detected")
        
        return True
        
    except Exception as e:
        print(f"  ❌ Error checking local file dependencies: {e}")
        return False

def test_firebase_exclusive_access():
    """Test that data access is exclusively through Firebase"""
    print("\n🧪 Testing Firebase Exclusive Access...")
    
    try:
        from src.bots.management_bot import (
            refresh_personnel_data,
            refresh_analytics_data
        )
        from src.utils.delivery_personnel_utils import refresh_delivery_personnel_data
        
        # Test real-time refresh functions
        print("  🔄 Testing real-time refresh functions...")
        
        personnel_data = refresh_personnel_data()
        print(f"    ✅ refresh_personnel_data(): {len(personnel_data)} records")
        
        analytics_data = refresh_analytics_data()
        total_analytics = sum(len(v) if isinstance(v, dict) else 0 for v in analytics_data.values())
        print(f"    ✅ refresh_analytics_data(): {total_analytics} total records")
        
        delivery_refresh = refresh_delivery_personnel_data()
        print(f"    ✅ refresh_delivery_personnel_data(): {delivery_refresh}")
        
        # Test direct Firebase access
        print("  🔥 Testing direct Firebase access...")
        from src.firebase_db import get_data, set_data
        
        # Test read operation
        test_data = get_data("system_health")
        print(f"    ✅ Firebase read test: {test_data is not None}")
        
        # Test write operation (safe test)
        test_write = set_data("test_verification", {"timestamp": "2025-07-10", "test": True})
        print(f"    ✅ Firebase write test: {test_write}")
        
        # Clean up test data
        from src.firebase_db import delete_data
        delete_data("test_verification")
        print(f"    🗑️ Test data cleaned up")
        
        return True
        
    except Exception as e:
        print(f"  ❌ Error testing Firebase exclusive access: {e}")
        return False

def main():
    """Run comprehensive Firebase exclusive access verification"""
    print("🚀 Firebase Firestore Exclusive Access Verification")
    print("=" * 70)
    
    results = {
        "collections": analyze_firebase_collections(),
        "operations": check_firebase_operations_usage(),
        "sync": verify_data_models_firebase_sync(),
        "local_files": check_local_file_dependencies(),
        "exclusive_access": test_firebase_exclusive_access()
    }
    
    print("\n" + "=" * 70)
    print("📊 VERIFICATION SUMMARY")
    print("=" * 70)
    
    # Collections summary
    if results["collections"]:
        existing_collections = sum(1 for c in results["collections"].values() if c.get("exists", False))
        total_collections = len(results["collections"])
        print(f"🔥 Firebase Collections: {existing_collections}/{total_collections} accessible")
    
    # Operations summary
    if results["operations"]:
        total_firebase_calls = sum(r.get("total_firebase_calls", 0) for r in results["operations"].values())
        files_with_local_storage = sum(1 for r in results["operations"].values() if r.get("local_storage_patterns"))
        print(f"🔧 Firebase Operations: {total_firebase_calls} total calls across all files")
        print(f"⚠️  Local Storage Patterns: {files_with_local_storage} files with potential local storage")
    
    # Sync summary
    if results["sync"]:
        synced_models = sum(1 for s in results["sync"].values() if s.get("synchronized", False))
        total_models = len(results["sync"])
        print(f"🔄 Data Model Sync: {synced_models}/{total_models} models synchronized with Firebase")
    
    # Overall assessment
    firebase_exclusive = (
        results["exclusive_access"] and
        results["local_files"] and
        all(s.get("synchronized", False) for s in results.get("sync", {}).values())
    )
    
    print(f"\n🎯 FIREBASE EXCLUSIVE ACCESS: {'✅ VERIFIED' if firebase_exclusive else '❌ ISSUES FOUND'}")
    
    if firebase_exclusive:
        print("✅ All data is properly stored in and accessed exclusively from Firebase Firestore")
    else:
        print("⚠️  Issues found - review details above for local storage dependencies")
    
    return firebase_exclusive

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
