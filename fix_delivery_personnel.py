#!/usr/bin/env python3
"""
Fix delivery personnel data for Telegram ID 1133538088
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from src.firebase_db import get_data, set_data

def fix_delivery_personnel():
    """Fix delivery personnel data for Telegram ID 1133538088"""
    print("=== FIXING DELIVERY PERSONNEL DATA ===")
    
    target_telegram_id = "1133538088"
    target_personnel_id = "dp_31fe5be0"
    
    # 1. Fix personnel status
    print(f"1. Updating personnel status to 'available'...")
    personnel_data = get_data(f"delivery_personnel/{target_personnel_id}")
    if personnel_data:
        personnel_data['status'] = 'available'
        personnel_data['is_verified'] = True
        set_data(f"delivery_personnel/{target_personnel_id}", personnel_data)
        print(f"✅ Updated personnel status to 'available'")
    else:
        print(f"❌ Personnel data not found for {target_personnel_id}")
        return False
    
    # 2. Set availability data
    print(f"2. Setting availability data...")
    set_data(f"delivery_personnel_availability/{target_personnel_id}", "available")
    print(f"✅ Set availability to 'available'")
    
    # 3. Set capacity data
    print(f"3. Setting capacity data...")
    set_data(f"delivery_personnel_capacity/{target_personnel_id}", 0)
    print(f"✅ Set capacity to 0")
    
    # 4. Set zones data (copy service areas)
    print(f"4. Setting zones data...")
    service_areas = personnel_data.get('service_areas', ['1', '2', '3', '4'])
    set_data(f"delivery_personnel_zones/{target_personnel_id}", service_areas)
    print(f"✅ Set zones to {service_areas}")
    
    # 5. Verify the fix
    print(f"\n=== VERIFICATION ===")
    
    # Check personnel data
    updated_personnel = get_data(f"delivery_personnel/{target_personnel_id}")
    availability = get_data(f"delivery_personnel_availability/{target_personnel_id}")
    capacity = get_data(f"delivery_personnel_capacity/{target_personnel_id}")
    zones = get_data(f"delivery_personnel_zones/{target_personnel_id}")
    
    print(f"Personnel Status: {updated_personnel.get('status')}")
    print(f"Personnel Verified: {updated_personnel.get('is_verified')}")
    print(f"Availability: {availability}")
    print(f"Capacity: {capacity}")
    print(f"Zones: {zones}")
    
    # Check availability criteria
    is_available = (
        updated_personnel.get('status') == 'available' and
        updated_personnel.get('is_verified') == True and
        availability == 'available' and
        isinstance(capacity, int) and capacity < updated_personnel.get('max_capacity', 5)
    )
    
    print(f"\n🎯 AVAILABILITY CHECK:")
    print(f"  Status OK: {updated_personnel.get('status') == 'available'}")
    print(f"  Verified: {updated_personnel.get('is_verified') == True}")
    print(f"  Available: {availability == 'available'}")
    print(f"  Capacity OK: {isinstance(capacity, int) and capacity < updated_personnel.get('max_capacity', 5)}")
    print(f"  Overall Available: {is_available}")
    
    if is_available:
        print(f"\n✅ SUCCESS: Personnel {target_personnel_id} is now available for delivery!")
        return True
    else:
        print(f"\n❌ FAILED: Personnel {target_personnel_id} is still not available")
        return False

def test_find_available_personnel():
    """Test the find_available_personnel function"""
    print(f"\n=== TESTING FIND_AVAILABLE_PERSONNEL ===")
    
    try:
        from src.utils.delivery_personnel_utils import find_available_personnel
        
        # Test with different area IDs
        test_areas = ['1', '2', '3', '4', '5']
        
        for area_id in test_areas:
            available_personnel = find_available_personnel(area_id)
            print(f"Area {area_id}: {len(available_personnel)} personnel - {available_personnel}")
            
            # Check if our target personnel is included
            if "dp_31fe5be0" in available_personnel:
                print(f"  ✅ dp_31fe5be0 (1133538088) is available for area {area_id}")
            else:
                print(f"  ❌ dp_31fe5be0 (1133538088) is NOT available for area {area_id}")
        
        return True
        
    except Exception as e:
        print(f"❌ Error testing find_available_personnel: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_delivery_broadcast_simulation():
    """Simulate the delivery broadcast logic"""
    print(f"\n=== SIMULATING DELIVERY BROADCAST ===")
    
    try:
        from src.utils.delivery_personnel_utils import find_available_personnel, delivery_personnel
        
        # Simulate order with area_id = "1"
        test_area_id = "1"
        available_personnel_ids = find_available_personnel(test_area_id)
        
        print(f"Order area: {test_area_id}")
        print(f"Available personnel: {available_personnel_ids}")
        
        if not available_personnel_ids:
            print(f"❌ No available personnel found for area {test_area_id}")
            return False
        
        # Check if our target personnel would receive the broadcast
        target_personnel_id = "dp_31fe5be0"
        if target_personnel_id in available_personnel_ids:
            print(f"✅ Personnel {target_personnel_id} (1133538088) would receive broadcast")
            
            # Get personnel data for broadcast
            personnel_data = delivery_personnel.get(target_personnel_id)
            if personnel_data:
                telegram_id = personnel_data.get('telegram_id')
                print(f"  Telegram ID: {telegram_id}")
                print(f"  Name: {personnel_data.get('name')}")
                print(f"  Service Areas: {personnel_data.get('service_areas')}")
                return True
            else:
                print(f"❌ Personnel data not found for {target_personnel_id}")
                return False
        else:
            print(f"❌ Personnel {target_personnel_id} (1133538088) would NOT receive broadcast")
            return False
            
    except Exception as e:
        print(f"❌ Error simulating delivery broadcast: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    print("🔧 FIXING DELIVERY PERSONNEL ISSUES")
    print("=" * 60)
    
    # Step 1: Fix the personnel data
    fix_success = fix_delivery_personnel()
    
    if fix_success:
        # Step 2: Test the find_available_personnel function
        test_success = test_find_available_personnel()
        
        if test_success:
            # Step 3: Simulate delivery broadcast
            broadcast_success = test_delivery_broadcast_simulation()
            
            if broadcast_success:
                print(f"\n🎉 ALL FIXES SUCCESSFUL!")
                print(f"Personnel 1133538088 should now receive order broadcasts!")
            else:
                print(f"\n⚠️  Broadcast simulation failed")
        else:
            print(f"\n⚠️  Function testing failed")
    else:
        print(f"\n❌ Personnel fix failed")
