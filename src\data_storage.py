"""
Data storage functions for the Wiz Aroma Delivery Bot.
Contains functions for loading and saving persistent data.
"""

import json
import datetime
import os
from typing import Dict, List, Any, Optional
import logging
import copy

# Import Firebase functions
from src.firebase_db import (
    get_user_points,
    update_user_points,
    update_user_points_batch,
    get_user_names,
    update_user_name,
    update_user_names_batch,
    get_user_phone_numbers,
    update_user_phone_number,
    update_user_phone_numbers_batch,
    get_user_emails,
    update_user_email,
    update_user_emails_batch,
    get_user_order_history,
    add_order_to_history,
    update_user_order_history_batch,

    update_favorite_orders,
    add_favorite_order,
    delete_favorite_order,
    update_favorite_orders_batch,
    get_current_orders,
    update_current_order,
    delete_current_order,
    update_current_orders_batch,
    get_order_status,
    update_order_status,
    update_order_status_batch,
    get_pending_admin_reviews,
    add_pending_admin_review,
    delete_pending_admin_review,
    update_pending_admin_reviews_batch,
    get_admin_remarks,
    update_admin_remark,
    update_admin_remarks_batch,
    get_awaiting_receipt,
    add_awaiting_receipt,
    delete_awaiting_receipt,
    update_awaiting_receipt_batch,
    get_data,
    set_data,
    update_data,
)

from src.config import (
    POINTS_FILE,
    ORDER_HISTORY_FILE,
    USER_NAMES_FILE,
    USER_PHONE_NUMBERS_FILE,
    USER_EMAILS_FILE,
    AREAS_FILE,
    RESTAURANTS_FILE,
    MENUS_FILE,
    DELIVERY_LOCATIONS_FILE,
    DELIVERY_FEES_FILE,
    FAVORITE_ORDERS_FILE,
    CURRENT_ORDERS_FILE,
    ORDER_STATUS_FILE,
    PENDING_ADMIN_REVIEWS_FILE,
    ADMIN_REMARKS_FILE,
    AWAITING_RECEIPT_FILE,
    DELIVERY_LOCATIONS_TEMP_FILE,
    USER_ORDER_COUNTS_FILE,
    CURRENT_ORDER_NUMBERS_FILE,
    logger,
)

from src.data_models import (
    user_points,
    user_order_history,
    user_names,
    user_phone_numbers,
    user_emails,
    orders,
    order_status,
    pending_admin_reviews,
    admin_remarks,
    awaiting_receipt,
    delivery_locations,
    current_order_numbers,
    favorite_orders,
    areas_data,
    restaurants_data,
    menus_data,
    delivery_locations_data,
    delivery_fees_data,
    delivery_personnel,
    delivery_personnel_assignments,
    delivery_personnel_availability,
    delivery_personnel_capacity,
    delivery_personnel_zones,
    delivery_personnel_performance,
    delivery_personnel_earnings,
)

# Set this to always use Firebase
USE_FIREBASE = True

# Initialize empty dictionaries to temporarily store loaded data
areas_data = {"areas": []}
restaurants_data = {"restaurants": []}
menus_data = {"default_menu_items": [], "restaurant_menus": {}}
delivery_locations_data = {"delivery_locations": []}
delivery_fees_data = {"delivery_fees": []}


def clean_order_history_data():
    """Remove 'is_creating_favorite' field from all order history data"""
    try:
        # Load order history data
        order_history = load_order_history()

        # Clean the data
        for user_id, orders in order_history.items():
            for order in orders:
                if "is_creating_favorite" in order:
                    del order["is_creating_favorite"]

        # Save the cleaned data
        update_user_order_history_batch(order_history)
    except Exception as e:
        logger.error(f"Error cleaning order history data: {e}")


def initialize_areas_from_config():
    """Initialize areas_data from the static config if no areas exist"""
    global areas_data

    # Only initialize if no areas exist
    if not areas_data.get("areas", []):
        areas = []
        area_id = 1

        # Get the area names from the static config
        from src.config import restaurants as config_restaurants

        for area_name in config_restaurants:
            # Add the area
            areas.append({"id": area_id, "name": area_name})
            area_id += 1

        # Update areas_data
        areas_data["areas"] = areas

        # Save to Firebase
        save_areas_data()

        logger.info(f"Initialized {len(areas)} areas from static config")
    return areas_data


def initialize_restaurants_from_config():
    """Initialize restaurants_data from the static config if no restaurants exist"""
    global restaurants_data, areas_data

    # Only initialize if no restaurants exist
    if not restaurants_data.get("restaurants", []):
        restaurants = []

        # Get the restaurants from the static config
        from src.config import restaurants as config_restaurants

        # First, ensure areas are initialized
        areas = areas_data.get("areas", [])
        if not areas:
            initialize_areas_from_config()
            areas = areas_data.get("areas", [])

        # Create a mapping of area names to IDs
        area_name_to_id = {area["name"]: area["id"] for area in areas}

        # Now add all restaurants from the config
        for area_name, area_restaurants in config_restaurants.items():
            area_id = area_name_to_id.get(area_name)
            if area_id:
                for restaurant_id, restaurant_info in area_restaurants.items():
                    # Add the restaurant
                    restaurants.append(
                        {
                            "id": int(restaurant_id),
                            "name": restaurant_info["name"],
                            "area_id": area_id,
                        }
                    )

        # Update restaurants_data
        restaurants_data["restaurants"] = restaurants

        # Save to Firebase
        save_restaurants_data()

        logger.info(f"Initialized {len(restaurants)} restaurants from static config")
    return restaurants_data


def load_user_data():
    """Load all user data from Firebase - only loads the necessary configuration data"""
    global areas_data, restaurants_data, menus_data, delivery_locations_data, delivery_fees_data, favorite_orders, user_points, user_order_history, user_names, user_phone_numbers, user_emails
    global delivery_personnel, delivery_personnel_assignments, delivery_personnel_availability, delivery_personnel_capacity, delivery_personnel_zones, delivery_personnel_performance

    # Load from Firebase
    # Load user points
    points_data = get_user_points()
    user_points.update(points_data)
    logger.info(f"Loaded points for {len(points_data)} users from Firebase")

    # Load order history
    order_history_data = get_user_order_history()
    user_order_history.update(order_history_data)
    logger.info(
        f"Loaded order history for {len(order_history_data)} users from Firebase"
    )

    # Load user names
    names_data = get_user_names()
    user_names.update(names_data)
    logger.info(f"Loaded names for {len(names_data)} users from Firebase")

    # Load user phone numbers
    phone_numbers_data = get_user_phone_numbers()
    user_phone_numbers.update(phone_numbers_data)
    logger.info(
        f"Loaded phone numbers for {len(phone_numbers_data)} users from Firebase"
    )

    # Load user emails
    emails_data = get_user_emails()
    user_emails.update(emails_data)
    logger.info(f"Loaded emails for {len(emails_data)} users from Firebase")

    # Load favorite orders
    try:
        # Get from Firebase using the Firebase function (import it specifically)
        from src.firebase_db import get_favorite_orders as get_firebase_favorite_orders
        favorites_data = get_firebase_favorite_orders()
        favorite_orders.update(favorites_data)
        logger.info(
            f"Loaded favorite orders for {len(favorites_data)} users from Firebase"
        )
    except Exception as e:
        logger.error(f"Error loading favorite orders: {e}")
        # Initialize empty dictionary to avoid errors
        favorite_orders.update({})
        logger.info("Initialized empty favorite orders dictionary")

    # Load configuration data
    areas_data = get_data("areas") or {"areas": []}
    restaurants_data = get_data("restaurants") or {"restaurants": []}

    # Load menus data with additional validation
    try:
        menus_data_from_firebase = get_data("menus")
        if menus_data_from_firebase and isinstance(menus_data_from_firebase, dict):
            # Ensure restaurant_menus exists and is a dictionary
            if "restaurant_menus" not in menus_data_from_firebase or not isinstance(
                menus_data_from_firebase["restaurant_menus"], dict
            ):
                logger.warning(
                    "restaurant_menus missing or not a dictionary in Firebase data. Initializing..."
                )
                menus_data_from_firebase["restaurant_menus"] = {}

            # Ensure default_menu_items exists
            if "default_menu_items" not in menus_data_from_firebase:
                logger.warning(
                    "default_menu_items missing in Firebase data. Initializing..."
                )
                menus_data_from_firebase["default_menu_items"] = []

            menus_data = menus_data_from_firebase
        else:
            logger.warning(
                "Invalid or missing menus_data from Firebase. Initializing with defaults..."
            )
            menus_data = {
                "default_menu_items": [],
                "restaurant_menus": {},
            }
    except Exception as e:
        logger.error(f"Error loading menus data: {e}")
        menus_data = {
            "default_menu_items": [],
            "restaurant_menus": {},
        }

    logger.info(
        f"Loaded menus data with {len(menus_data.get('restaurant_menus', {}))} restaurant menus"
    )

    delivery_locations_data = get_data("delivery_locations") or {
        "delivery_locations": []
    }
    delivery_fees_data = get_data("delivery_fees") or {"delivery_fees": []}

    # Initialize areas and restaurants from config if they're empty
    if not areas_data.get("areas", []):
        initialize_areas_from_config()

    if not restaurants_data.get("restaurants", []):
        initialize_restaurants_from_config()

    # Load delivery personnel data
    try:
        delivery_personnel.clear()
        delivery_personnel.update(load_delivery_personnel_data())
        logger.info(f"Loaded {len(delivery_personnel)} delivery personnel records")

        delivery_personnel_assignments.clear()
        delivery_personnel_assignments.update(load_delivery_personnel_assignments_data())
        logger.info(f"Loaded {len(delivery_personnel_assignments)} delivery assignments")

        delivery_personnel_availability.clear()
        delivery_personnel_availability.update(load_delivery_personnel_availability_data())
        logger.info(f"Loaded availability for {len(delivery_personnel_availability)} personnel")

        delivery_personnel_capacity.clear()
        delivery_personnel_capacity.update(load_delivery_personnel_capacity_data())
        logger.info(f"Loaded capacity for {len(delivery_personnel_capacity)} personnel")

        delivery_personnel_zones.clear()
        delivery_personnel_zones.update(load_delivery_personnel_zones_data())
        logger.info(f"Loaded zones for {len(delivery_personnel_zones)} personnel")

        delivery_personnel_performance.clear()
        delivery_personnel_performance.update(load_delivery_personnel_performance_data())
        logger.info(f"Loaded performance data for {len(delivery_personnel_performance)} personnel")

    except Exception as e:
        logger.error(f"Error loading delivery personnel data: {e}")
        # Initialize empty data structures if loading fails
        delivery_personnel.clear()
        delivery_personnel_assignments.clear()
        delivery_personnel_availability.clear()
        delivery_personnel_capacity.clear()
        delivery_personnel_zones.clear()
        delivery_personnel_performance.clear()

    logger.info("Finished loading all user data from Firebase")


def initialize_data_files():
    """Initialize all data files with empty data if they don't exist"""
    # This is a no-op as we're using Firebase now
    pass


def backup_file(filename):
    """Create a backup of a file if it exists"""
    # This is a no-op as we're using Firebase now
    pass


# New load functions for previously in-memory data
def load_points() -> Dict[str, int]:
    """Load points data from Firebase"""
    # Get from Firebase
    loaded_points = get_user_points()
    # Update in-memory points
    user_points.update(loaded_points)
    return loaded_points


def load_order_history() -> Dict[str, List[Dict[str, Any]]]:
    """Load order history data from Firebase"""
    # Get from Firebase
    order_history_data = get_user_order_history()
    return order_history_data


def load_user_names() -> Dict[str, str]:
    """Load user names data from Firebase"""
    # Get from Firebase
    names_data = get_user_names()
    return names_data


def load_user_phone_numbers() -> Dict[str, str]:
    """Load user phone numbers data from Firebase"""
    # Get from Firebase
    phone_numbers_data = get_user_phone_numbers()
    return phone_numbers_data


def load_user_emails() -> Dict[str, str]:
    """Load user email addresses data from Firebase"""
    # Get from Firebase
    emails_data = get_user_emails()
    return emails_data


def load_favorite_orders() -> Dict[str, List[Dict[str, Any]]]:
    """Load favorite orders data from Firebase"""
    # Get from Firebase using the Firebase function
    from src.firebase_db import get_favorite_orders as get_firebase_favorite_orders
    favorites_data = get_firebase_favorite_orders()
    return favorites_data


def load_current_orders() -> Dict[str, Dict[str, Any]]:
    """Load current orders data from Firebase"""
    # Get from Firebase
    current_orders_data = get_current_orders()
    return current_orders_data


def load_order_status() -> Dict[str, str]:
    """Load order status data from Firebase"""
    # Get from Firebase
    order_status_data = get_order_status()
    return order_status_data


def load_pending_admin_reviews() -> Dict[str, Dict[str, Any]]:
    """Load pending admin reviews data from Firebase"""
    # Get from Firebase
    pending_admin_reviews_data = get_pending_admin_reviews()
    return pending_admin_reviews_data


def load_admin_remarks() -> Dict[str, str]:
    """Load admin remarks data from Firebase"""
    # Get from Firebase
    admin_remarks_data = get_admin_remarks()
    return admin_remarks_data


def load_awaiting_receipt() -> Dict[str, Any]:
    """Load awaiting receipt data from Firebase"""
    # Get from Firebase
    awaiting_receipt_data = get_awaiting_receipt()
    return awaiting_receipt_data


def load_delivery_locations_temp() -> Dict[str, str]:
    """Load temporary delivery locations data from Firebase"""
    # Get from Firebase
    locations_data = get_data("delivery_locations_temp") or {}
    return locations_data


def load_user_order_counts() -> Dict[str, int]:
    """Load user order counts data from Firebase"""
    # Get from Firebase
    order_counts_data = get_data("user_order_counts") or {}
    return order_counts_data


def load_current_order_numbers() -> Dict[str, str]:
    """Load current order numbers data from Firebase"""
    # Get from Firebase
    current_order_numbers_data = get_data("current_order_numbers") or {}
    return current_order_numbers_data


def load_delivery_personnel_data() -> Dict[str, Dict[str, Any]]:
    """Load delivery personnel data from Firebase"""
    # Get from Firebase
    personnel_data = get_data("delivery_personnel")
    return personnel_data if personnel_data else {}


def load_delivery_personnel_assignments_data() -> Dict[str, Dict[str, Any]]:
    """Load delivery personnel assignments from Firebase"""
    # Get from Firebase
    assignments_data = get_data("delivery_personnel_assignments")
    return assignments_data if assignments_data else {}


def load_delivery_personnel_assignments() -> Dict[str, Dict[str, Any]]:
    """Alias for load_delivery_personnel_assignments_data for consistency"""
    return load_delivery_personnel_assignments_data()


def load_delivery_personnel_availability_data() -> Dict[str, str]:
    """Load delivery personnel availability from Firebase"""
    # Get from Firebase
    availability_data = get_data("delivery_personnel_availability")
    return availability_data if availability_data else {}


def load_delivery_personnel_capacity_data() -> Dict[str, int]:
    """Load delivery personnel capacity from Firebase"""
    # Get from Firebase
    capacity_data = get_data("delivery_personnel_capacity")
    return capacity_data if capacity_data else {}


def load_delivery_personnel_zones_data() -> Dict[str, List[str]]:
    """Load delivery personnel zones from Firebase"""
    # Get from Firebase
    zones_data = get_data("delivery_personnel_zones")
    return zones_data if zones_data else {}


def load_delivery_personnel_performance_data() -> Dict[str, Dict[str, Any]]:
    """Load delivery personnel performance from Firebase"""
    # Get from Firebase
    performance_data = get_data("delivery_personnel_performance")
    return performance_data if performance_data else {}


def load_delivery_personnel_earnings_data() -> Dict[str, Dict[str, Any]]:
    """Load delivery personnel earnings from Firebase"""
    # Get from Firebase
    earnings_data = get_data("delivery_personnel_earnings")
    return earnings_data if earnings_data else {}


def _load_json_file(filename: str, default_value: Any) -> Any:
    """Legacy function kept for compatibility"""
    return default_value


# Save functions for previously in-memory data
def save_points(data: Dict[str, int]) -> bool:
    """Save points data to Firebase"""
    # Save to Firebase
    return update_user_points_batch(data)


def save_order_history(data: Dict[str, List[Dict[str, Any]]]) -> bool:
    """Save order history data to Firebase"""
    # Save to Firebase
    return update_user_order_history_batch(data)


def save_user_names(data: Dict[str, str]) -> bool:
    """Save user names data to Firebase"""
    # Save to Firebase
    return update_user_names_batch(data)


def save_user_phone_numbers(data: Dict[str, str]) -> bool:
    """Save user phone numbers data to Firebase"""
    # Save to Firebase
    return update_user_phone_numbers_batch(data)


def save_user_emails(data: Dict[str, str]) -> bool:
    """Save user email addresses data to Firebase"""
    # Save to Firebase
    return update_user_emails_batch(data)


def save_favorite_orders(data: Dict[str, List[Dict[str, Any]]]) -> bool:
    """Save favorite orders data to Firebase"""
    # Save to Firebase
    return update_favorite_orders_batch(data)


def save_current_orders(data: Dict[str, Dict[str, Any]]) -> bool:
    """Save current orders data to Firebase"""
    # Save to Firebase
    return update_current_orders_batch(data)


def save_order_status(data: Dict[str, str]) -> bool:
    """Save order status data to Firebase"""
    # Save to Firebase
    return update_order_status_batch(data)


def save_pending_admin_reviews(data: Dict[str, Dict[str, Any]]) -> bool:
    """Save pending admin reviews data to Firebase"""
    # Save to Firebase
    return update_pending_admin_reviews_batch(data)


def save_admin_remarks(data: Dict[str, str]) -> bool:
    """Save admin remarks data to Firebase"""
    # Save to Firebase
    return update_admin_remarks_batch(data)


def save_awaiting_receipt(data: Dict[str, Any]) -> bool:
    """Save awaiting receipt data to Firebase"""
    # Save to Firebase
    return update_awaiting_receipt_batch(data)


def save_delivery_locations_temp(data: Dict[str, str]) -> bool:
    """Save temporary delivery locations data to Firebase"""
    # Save to Firebase
    return update_data("delivery_locations_temp", data)


def save_user_order_counts(data: Dict[str, int]) -> bool:
    """Save user order counts data to Firebase"""
    # Save to Firebase
    return update_data("user_order_counts", data)


def save_current_order_numbers(data: Dict[str, str]) -> bool:
    """Save current order numbers data to Firebase"""
    # Save to Firebase
    return update_data("current_order_numbers", data)


def save_user_data():
    """Save all user data to Firebase"""
    try:
        # Save user data to Firebase
        save_points(user_points)
        save_user_names(user_names)
        save_user_phone_numbers(user_phone_numbers)
        save_user_emails(user_emails)
        save_favorite_orders(favorite_orders)

        logger.info("User data saved successfully")
        return True
    except Exception as e:
        logger.error(f"Error in save_user_data: {e}")
        return False


def save_areas_data():
    """Save areas data to Firebase"""
    global areas_data
    # Save to Firebase
    return update_data("areas", areas_data)


def save_restaurants_data():
    """Save restaurants data to Firebase"""
    global restaurants_data
    # Save to Firebase
    return update_data("restaurants", restaurants_data)


def save_menus_data():
    """Save menus data to Firebase"""
    global menus_data
    # Save to Firebase
    return update_data("menus", menus_data)


def save_delivery_locations_data():
    """Save delivery locations data to Firebase"""
    global delivery_locations_data
    # Save to Firebase
    return update_data("delivery_locations", delivery_locations_data)


def save_delivery_fees_data():
    """Save delivery fees data to Firebase"""
    global delivery_fees_data
    # Save to Firebase
    return update_data("delivery_fees", delivery_fees_data)


def save_delivery_personnel():
    """Save delivery personnel data to Firebase"""
    global delivery_personnel
    # Save to Firebase
    return update_data("delivery_personnel", delivery_personnel)


def save_delivery_personnel_assignments(data: Dict[str, Dict[str, Any]] = None):
    """Save delivery personnel assignments to Firebase"""
    global delivery_personnel_assignments
    if data is not None:
        delivery_personnel_assignments.clear()
        delivery_personnel_assignments.update(data)
    # Save to Firebase
    return update_data("delivery_personnel_assignments", delivery_personnel_assignments)


def save_delivery_personnel_availability():
    """Save delivery personnel availability to Firebase"""
    global delivery_personnel_availability
    # Save to Firebase
    return update_data("delivery_personnel_availability", delivery_personnel_availability)


def save_delivery_personnel_capacity():
    """Save delivery personnel capacity to Firebase"""
    global delivery_personnel_capacity
    # Save to Firebase
    return update_data("delivery_personnel_capacity", delivery_personnel_capacity)


def save_delivery_personnel_zones():
    """Save delivery personnel zones to Firebase"""
    global delivery_personnel_zones
    # Save to Firebase
    return update_data("delivery_personnel_zones", delivery_personnel_zones)


def save_delivery_personnel_performance():
    """Save delivery personnel performance to Firebase"""
    global delivery_personnel_performance
    # Save to Firebase
    return update_data("delivery_personnel_performance", delivery_personnel_performance)


def save_delivery_personnel_earnings(data: Dict[str, Dict[str, Any]] = None):
    """Save delivery personnel earnings to Firebase with error handling"""
    try:
        global delivery_personnel_earnings
        if data is not None:
            # Validate data structure
            if not isinstance(data, dict):
                logger.error("Invalid data type for delivery personnel earnings: expected dict")
                return False

            delivery_personnel_earnings.clear()
            delivery_personnel_earnings.update(data)

        # Save to Firebase with error handling
        success = update_data("delivery_personnel_earnings", delivery_personnel_earnings)
        if not success:
            logger.error("Failed to save delivery personnel earnings to Firebase")
        return success

    except Exception as e:
        logger.error(f"Error saving delivery personnel earnings: {e}")
        return False


def _safe_write_json(file_path: str, data: Any) -> bool:
    """Legacy function kept for compatibility but now a no-op"""
    logger.debug(f"JSON write operation ignored, using Firebase instead")
    return True


# Area CRUD operations
def get_all_areas():
    """Get all areas"""
    global areas_data
    return areas_data.get("areas", [])


def get_area_by_id(area_id):
    """Get area by ID"""
    try:
        # Ensure area_id is an integer
        area_id = int(area_id)

        for area in get_all_areas():
            try:
                # Convert area id to integer for comparison
                if int(area["id"]) == area_id:
                    return area
            except (ValueError, TypeError):
                # Skip areas with non-integer IDs
                continue

        return None
    except (ValueError, TypeError):
        # If area_id can't be converted to int, try string comparison
        area_id_str = str(area_id)
        for area in get_all_areas():
            if str(area["id"]) == area_id_str:
                return area
        return None


def add_area(name):
    """Add a new area"""
    global areas_data
    areas = areas_data.get("areas", [])
    # Generate a new ID (max ID + 1)
    new_id = 1
    if areas:
        new_id = max(area["id"] for area in areas) + 1

    new_area = {"id": new_id, "name": name}

    areas.append(new_area)
    areas_data["areas"] = areas
    save_areas_data()
    return new_area


def update_area(area_id, name):
    """Update an existing area"""
    try:
        # Ensure area_id is an integer
        area_id = int(area_id)

        global areas_data
        for area in areas_data.get("areas", []):
            try:
                # Convert area id to integer for comparison
                if int(area["id"]) == area_id:
                    area["name"] = name
                    save_areas_data()
                    return area
            except (ValueError, TypeError):
                # Skip areas with non-integer IDs
                continue

        return None
    except (ValueError, TypeError):
        # If area_id can't be converted to int, try string comparison
        area_id_str = str(area_id)
        for area in areas_data.get("areas", []):
            if str(area["id"]) == area_id_str:
                area["name"] = name
                save_areas_data()
                return area
        return None


def delete_area(area_id):
    """Delete an area"""
    try:
        # Ensure area_id is an integer
        area_id = int(area_id)

        global areas_data
        areas = areas_data.get("areas", [])
        for i, area in enumerate(areas):
            try:
                # Convert area id to integer for comparison
                if int(area["id"]) == area_id:
                    del areas[i]
                    areas_data["areas"] = areas
                    save_areas_data()
                    return True
            except (ValueError, TypeError):
                # Skip areas with non-integer IDs
                continue

        return False
    except (ValueError, TypeError):
        # If area_id can't be converted to int, try string comparison
        area_id_str = str(area_id)
        for i, area in enumerate(areas):
            if str(area["id"]) == area_id_str:
                del areas[i]
                areas_data["areas"] = areas
                save_areas_data()
                return True
        return False


# Restaurant CRUD operations
def get_all_restaurants():
    """Get all restaurants"""
    global restaurants_data
    return restaurants_data.get("restaurants", [])


def get_restaurants_by_area(area_id):
    """Get restaurants by area ID"""
    try:
        # Ensure area_id is an integer
        area_id = int(area_id)

        restaurants = []
        for restaurant in get_all_restaurants():
            try:
                # Convert restaurant area_id to integer for comparison
                restaurant_area_id = int(restaurant["area_id"])
                if restaurant_area_id == area_id:
                    restaurants.append(restaurant)
            except (ValueError, TypeError):
                # Skip restaurants with non-integer area_id
                continue

        return restaurants
    except (ValueError, TypeError):
        # If area_id can't be converted to int, try string comparison
        area_id_str = str(area_id)
        restaurants = []
        for restaurant in get_all_restaurants():
            if str(restaurant["area_id"]) == area_id_str:
                restaurants.append(restaurant)
        return restaurants


def get_restaurant_by_id(restaurant_id):
    """Get restaurant by ID"""
    try:
        # Ensure restaurant_id is an integer
        restaurant_id = int(restaurant_id)

        for restaurant in get_all_restaurants():
            try:
                # Convert restaurant id to integer for comparison
                if int(restaurant["id"]) == restaurant_id:
                    return restaurant
            except (ValueError, TypeError):
                # Skip restaurants with non-integer IDs
                continue

        return None
    except (ValueError, TypeError):
        # If restaurant_id can't be converted to int, try string comparison
        restaurant_id_str = str(restaurant_id)
        for restaurant in get_all_restaurants():
            if str(restaurant["id"]) == restaurant_id_str:
                return restaurant
        return None


def add_restaurant(name, area_id):
    """Add a new restaurant"""
    global restaurants_data
    restaurants = restaurants_data.get("restaurants", [])
    # Generate a new ID (max ID + 1)
    new_id = 1
    if restaurants:
        new_id = max(restaurant["id"] for restaurant in restaurants) + 1

    new_restaurant = {"id": new_id, "name": name, "area_id": area_id}

    restaurants.append(new_restaurant)
    restaurants_data["restaurants"] = restaurants
    save_restaurants_data()
    return new_restaurant


def update_restaurant(restaurant_id, name=None, area_id=None):
    """Update an existing restaurant"""
    try:
        # Ensure restaurant_id is an integer
        restaurant_id = int(restaurant_id)

        global restaurants_data
        for restaurant in restaurants_data.get("restaurants", []):
            try:
                # Convert restaurant id to integer for comparison
                if int(restaurant["id"]) == restaurant_id:
                    if name is not None:
                        restaurant["name"] = name
                    if area_id is not None:
                        restaurant["area_id"] = area_id
                    save_restaurants_data()
                    return restaurant
            except (ValueError, TypeError):
                # Skip restaurants with non-integer IDs
                continue

        return None
    except (ValueError, TypeError):
        # If restaurant_id can't be converted to int, try string comparison
        restaurant_id_str = str(restaurant_id)
        for restaurant in restaurants_data.get("restaurants", []):
            if str(restaurant["id"]) == restaurant_id_str:
                if name is not None:
                    restaurant["name"] = name
                if area_id is not None:
                    restaurant["area_id"] = area_id
                save_restaurants_data()
                return restaurant
        return None


def delete_restaurant(restaurant_id):
    """Delete a restaurant"""
    try:
        # Ensure restaurant_id is an integer
        restaurant_id = int(restaurant_id)

        global restaurants_data
        restaurants = restaurants_data.get("restaurants", [])
        for i, restaurant in enumerate(restaurants):
            try:
                # Convert restaurant id to integer for comparison
                if int(restaurant["id"]) == restaurant_id:
                    del restaurants[i]
                    restaurants_data["restaurants"] = restaurants
                    save_restaurants_data()
                    return True
            except (ValueError, TypeError):
                # Skip restaurants with non-integer IDs
                continue

        return False
    except (ValueError, TypeError):
        # If restaurant_id can't be converted to int, try string comparison
        restaurant_id_str = str(restaurant_id)
        for i, restaurant in enumerate(restaurants):
            if str(restaurant["id"]) == restaurant_id_str:
                del restaurants[i]
                restaurants_data["restaurants"] = restaurants
                save_restaurants_data()
                return True
        return False


# Menu CRUD operations
def get_default_menu_items():
    """Get default menu items"""
    global menus_data
    return menus_data.get("default_menu_items", [])


def get_restaurant_menu(restaurant_id):
    """Get menu for a specific restaurant"""
    global menus_data
    restaurant_id_str = str(restaurant_id)

    # Debug logging to check menus_data structure
    logger.debug(f"menus_data type: {type(menus_data)}")
    logger.debug(f"menus_data contents: {menus_data}")

    # Ensure menus_data is a dictionary and has restaurant_menus as a dictionary
    if not isinstance(menus_data, dict):
        logger.error(f"menus_data is not a dictionary: {type(menus_data)}")
        menus_data = {"default_menu_items": [], "restaurant_menus": {}}

    # Initialize restaurant_menus if it doesn't exist or isn't a dictionary
    if "restaurant_menus" not in menus_data or not isinstance(
        menus_data["restaurant_menus"], dict
    ):
        logger.warning(f"restaurant_menus missing or not a dictionary. Initializing...")
        menus_data["restaurant_menus"] = {}

    restaurant_menus = menus_data.get("restaurant_menus", {})
    logger.debug(f"restaurant_menus type: {type(restaurant_menus)}")

    # Check if this restaurant has a menu in our database
    if restaurant_id_str in restaurant_menus and restaurant_menus[restaurant_id_str]:
        logger.info(
            f"Found existing menu for restaurant {restaurant_id} with {len(restaurant_menus[restaurant_id_str])} items"
        )
        return restaurant_menus[restaurant_id_str]
    else:
        # Try to import menus from the data/menus.py file
        try:
            from src.data.menus import menus as predefined_menus

            logger.debug(f"predefined_menus type: {type(predefined_menus)}")
            logger.debug(f"predefined_menus keys: {list(predefined_menus.keys())}")

            # Convert restaurant_id to int for lookup in predefined_menus
            try:
                restaurant_id_int = int(restaurant_id)
                if restaurant_id_int in predefined_menus:
                    # Copy the predefined menu to our database
                    logger.info(
                        f"Found menu in predefined_menus for restaurant {restaurant_id_int}"
                    )
                    restaurant_menus[restaurant_id_str] = predefined_menus[
                        restaurant_id_int
                    ]
                    menus_data["restaurant_menus"] = restaurant_menus
                    logger.debug(
                        f"Updated menus_data with menu for restaurant {restaurant_id_str}"
                    )
                    save_menus_data()
                    return restaurant_menus[restaurant_id_str]
                else:
                    logger.warning(
                        f"Restaurant ID {restaurant_id_int} not found in predefined_menus"
                    )
            except (ValueError, TypeError) as e:
                logger.error(f"Error converting restaurant_id to int: {e}")
                # If conversion fails, try string lookup (though this is unlikely to succeed)
                if restaurant_id_str in predefined_menus:
                    restaurant_menus[restaurant_id_str] = predefined_menus[
                        restaurant_id_str
                    ]
                    menus_data["restaurant_menus"] = restaurant_menus
                    save_menus_data()
                    return restaurant_menus[restaurant_id_str]
        except (ImportError, KeyError) as e:
            logger.warning(
                f"Could not load predefined menu for restaurant {restaurant_id}: {e}"
            )
        except Exception as e:
            logger.error(
                f"Unexpected error loading menu for restaurant {restaurant_id}: {e}",
                exc_info=True,
            )

        # If no predefined menu, return default items or an empty menu
        logger.info(f"Creating empty menu for restaurant {restaurant_id_str}")
        restaurant_menus[restaurant_id_str] = []
        menus_data["restaurant_menus"] = restaurant_menus
        save_menus_data()
        return restaurant_menus[restaurant_id_str]


def add_menu_item(restaurant_id, name, price):
    """Add a menu item to a restaurant"""
    global menus_data
    restaurant_id_str = str(restaurant_id)
    restaurant_menus = menus_data.get("restaurant_menus", {})

    # Initialize restaurant menu if it doesn't exist
    if restaurant_id_str not in restaurant_menus:
        restaurant_menus[restaurant_id_str] = []

    menu_items = restaurant_menus[restaurant_id_str]

    # Generate a new ID (max ID + 1)
    new_id = 1
    if menu_items:
        new_id = max(item["id"] for item in menu_items) + 1

    new_item = {"id": new_id, "name": name, "price": price}

    menu_items.append(new_item)
    menus_data["restaurant_menus"] = restaurant_menus
    save_menus_data()
    return new_item


def update_menu_item(restaurant_id, item_id, name=None, price=None):
    """Update a menu item"""
    global menus_data
    restaurant_id_str = str(restaurant_id)
    restaurant_menus = menus_data.get("restaurant_menus", {})

    if restaurant_id_str not in restaurant_menus:
        return None

    for item in restaurant_menus[restaurant_id_str]:
        if item["id"] == item_id:
            if name is not None:
                item["name"] = name
            if price is not None:
                item["price"] = price
            save_menus_data()
            return item

    return None


def delete_menu_item(restaurant_id, item_id):
    """Delete a menu item"""
    global menus_data
    restaurant_id_str = str(restaurant_id)
    restaurant_menus = menus_data.get("restaurant_menus", {})

    if restaurant_id_str not in restaurant_menus:
        return False

    menu_items = restaurant_menus[restaurant_id_str]
    for i, item in enumerate(menu_items):
        if item["id"] == item_id:
            del menu_items[i]
            save_menus_data()
            return True

    return False


# Delivery Location CRUD operations
def get_all_delivery_locations():
    """Get all delivery locations"""
    global delivery_locations_data
    return delivery_locations_data.get("delivery_locations", [])


def get_delivery_location_by_id(location_id):
    """Get delivery location by ID"""
    try:
        # Ensure location_id is an integer
        location_id = int(location_id)

        for location in get_all_delivery_locations():
            try:
                # Convert location id to integer for comparison
                if int(location["id"]) == location_id:
                    return location
            except (ValueError, TypeError):
                # Skip locations with non-integer IDs
                continue

        return None
    except (ValueError, TypeError):
        # If location_id can't be converted to int, try string comparison
        location_id_str = str(location_id)
        for location in get_all_delivery_locations():
            if str(location["id"]) == location_id_str:
                return location
        return None


def add_delivery_location(name):
    """Add a new delivery location"""
    global delivery_locations_data
    locations = delivery_locations_data.get("delivery_locations", [])
    # Generate a new ID (max ID + 1)
    new_id = 1
    if locations:
        new_id = max(location["id"] for location in locations) + 1

    new_location = {"id": new_id, "name": name}

    locations.append(new_location)
    delivery_locations_data["delivery_locations"] = locations
    save_delivery_locations_data()
    return new_location


def update_delivery_location(location_id, name):
    """Update an existing delivery location"""
    try:
        # Ensure location_id is an integer
        location_id = int(location_id)

        global delivery_locations_data
        for location in delivery_locations_data.get("delivery_locations", []):
            try:
                # Convert location id to integer for comparison
                if int(location["id"]) == location_id:
                    location["name"] = name
                    save_delivery_locations_data()
                    return location
            except (ValueError, TypeError):
                # Skip locations with non-integer IDs
                continue

        return None
    except (ValueError, TypeError):
        # If location_id can't be converted to int, try string comparison
        location_id_str = str(location_id)
        for location in delivery_locations_data.get("delivery_locations", []):
            if str(location["id"]) == location_id_str:
                location["name"] = name
                save_delivery_locations_data()
                return location
        return None


def delete_delivery_location(location_id):
    """Delete a delivery location"""
    try:
        # Ensure location_id is an integer
        location_id = int(location_id)

        global delivery_locations_data
        locations = delivery_locations_data.get("delivery_locations", [])
        for i, location in enumerate(locations):
            try:
                # Convert location id to integer for comparison
                if int(location["id"]) == location_id:
                    del locations[i]
                    delivery_locations_data["delivery_locations"] = locations
                    save_delivery_locations_data()
                    return True
            except (ValueError, TypeError):
                # Skip locations with non-integer IDs
                continue

        return False
    except (ValueError, TypeError):
        # If location_id can't be converted to int, try string comparison
        location_id_str = str(location_id)
        for i, location in enumerate(locations):
            if str(location["id"]) == location_id_str:
                del locations[i]
                delivery_locations_data["delivery_locations"] = locations
                save_delivery_locations_data()
                return True
        return False


# Delivery Fee CRUD operations
def get_all_delivery_fees():
    """Get all delivery fees"""
    global delivery_fees_data
    return delivery_fees_data.get("delivery_fees", [])


def get_delivery_fee(area_id, location_id):
    """Get delivery fee for a specific area and location"""
    try:
        # Ensure both IDs are integers
        area_id = int(area_id)
        location_id = int(location_id)

        logger.info(
            f"Looking for delivery fee - Area ID: {area_id} ({type(area_id)}), Location ID: {location_id} ({type(location_id)})"
        )
        all_fees = get_all_delivery_fees()
        logger.info(f"Total delivery fees available: {len(all_fees)}")

        for fee in all_fees:
            # Convert fee IDs to integers for comparison
            fee_area_id = int(fee["area_id"])
            fee_location_id = int(fee["location_id"])

            # This verbose logging is now filtered by LogFilter class
            logger.info(
                f"Checking fee: area_id={fee_area_id}, location_id={fee_location_id}, fee={fee['fee']}"
            )

            if fee_area_id == area_id and fee_location_id == location_id:
                # Use the new logger method for a cleaner summary
                logger.log_fee_summary(area_id, location_id, fee["fee"])
                return fee["fee"]

        logger.warning(f"No fee found for area_id={area_id}, location_id={location_id}")
        return 0  # Default fee
    except (ValueError, TypeError) as e:
        logger.error(f"Error in get_delivery_fee: {e}")
        logger.error(
            f"Arguments: area_id={area_id} ({type(area_id)}), location_id={location_id} ({type(location_id)})"
        )
        return 0  # Return default fee on error


def add_delivery_fee(area_id, location_id, fee):
    """Add or update a delivery fee"""
    try:
        # Ensure IDs are integers
        area_id_int = int(area_id)
        location_id_int = int(location_id)

        global delivery_fees_data
        fees = delivery_fees_data.get("delivery_fees", [])

        # Check if fee already exists
        for existing_fee in fees:
            try:
                # Convert fee IDs to integers for comparison
                fee_area_id = int(existing_fee["area_id"])
                fee_location_id = int(existing_fee["location_id"])

                if fee_area_id == area_id_int and fee_location_id == location_id_int:
                    existing_fee["fee"] = fee
                    save_delivery_fees_data()
                    return existing_fee
            except (ValueError, TypeError):
                # Skip fees with non-integer IDs
                continue

        # Add new fee - store original values to maintain data type consistency
        new_fee = {"area_id": area_id, "location_id": location_id, "fee": fee}

        fees.append(new_fee)
        delivery_fees_data["delivery_fees"] = fees
        save_delivery_fees_data()
        return new_fee
    except (ValueError, TypeError):
        # If IDs can't be converted to int, use string comparison
        area_id_str = str(area_id)
        location_id_str = str(location_id)

        for existing_fee in fees:
            if (
                str(existing_fee["area_id"]) == area_id_str
                and str(existing_fee["location_id"]) == location_id_str
            ):
                existing_fee["fee"] = fee
                save_delivery_fees_data()
                return existing_fee

        # Add new fee with string IDs
        new_fee = {"area_id": area_id, "location_id": location_id, "fee": fee}

        fees.append(new_fee)
        delivery_fees_data["delivery_fees"] = fees
        save_delivery_fees_data()
        return new_fee


def delete_delivery_fee(area_id, location_id):
    """Delete a delivery fee"""
    try:
        # Ensure IDs are integers
        area_id = int(area_id)
        location_id = int(location_id)

        global delivery_fees_data
        fees = delivery_fees_data.get("delivery_fees", [])
        for i, fee in enumerate(fees):
            try:
                # Convert fee IDs to integers for comparison
                fee_area_id = int(fee["area_id"])
                fee_location_id = int(fee["location_id"])

                if fee_area_id == area_id and fee_location_id == location_id:
                    del fees[i]
                    delivery_fees_data["delivery_fees"] = fees
                    save_delivery_fees_data()
                    return True
            except (ValueError, TypeError):
                # Skip fees with non-integer IDs
                continue

        return False
    except (ValueError, TypeError):
        # If IDs can't be converted to int, try string comparison
        area_id_str = str(area_id)
        location_id_str = str(location_id)

        for i, fee in enumerate(fees):
            if (
                str(fee["area_id"]) == area_id_str
                and str(fee["location_id"]) == location_id_str
            ):
                del fees[i]
                delivery_fees_data["delivery_fees"] = fees
                save_delivery_fees_data()
                return True
        return False


# Alias for compatibility with existing code
def save_all_user_data():
    """Alias for save_user_data"""
    return save_user_data()


def clean_up_order_data(user_id: int, order_number: Optional[str]):
    """Clean up all order-related data"""
    logger.info(f"Starting cleanup for order #{order_number}, user ID: {user_id}")

    # Convert user_id to string for safer dictionary operations
    user_id_str = str(user_id) if user_id is not None else None

    # Ensure order_number is valid
    if order_number is None:
        order_number = ""
        logger.warning("clean_up_order_data called with None order_number")

    # Track which operations succeeded and failed
    cleanup_results = {
        "pending_admin_reviews": False,
        "admin_remarks": False,
        "orders": False,
        "order_status": False,
        "awaiting_receipt": False,
        "current_order_numbers": False,
    }

    # Define a helper function to safely remove items from dictionaries
    def safe_dict_remove(data_dict, key, dict_name):
        try:
            if data_dict is not None and key in data_dict:
                del data_dict[key]
                cleanup_results[dict_name] = True
                logger.info(f"Removed user {key} from {dict_name} dict")
                return True
            return False
        except Exception as e:
            logger.error(f"Error removing {key} from {dict_name}: {e}")
            return False

    # Try to remove order from pending_admin_reviews
    try:
        if order_number and order_number in pending_admin_reviews:
            del pending_admin_reviews[order_number]
            cleanup_results["pending_admin_reviews"] = True
            logger.info(f"Removed order #{order_number} from pending_admin_reviews")
    except Exception as e:
        logger.error(f"Error removing order from pending_admin_reviews: {e}")

    # Try to remove order from admin_remarks
    try:
        if order_number and order_number in admin_remarks:
            del admin_remarks[order_number]
            cleanup_results["admin_remarks"] = True
            logger.info(f"Removed order #{order_number} from admin_remarks")
    except Exception as e:
        logger.error(f"Error removing order from admin_remarks: {e}")

    # Remove user's data from the remaining dictionaries
    if user_id is not None:
        # Remove from orders
        safe_dict_remove(orders, user_id, "orders")

        # Remove from order_status
        safe_dict_remove(order_status, user_id, "order_status")

        # Remove from awaiting_receipt
        safe_dict_remove(awaiting_receipt, user_id, "awaiting_receipt")

        # Remove from current_order_numbers
        safe_dict_remove(current_order_numbers, user_id, "current_order_numbers")

    # Now persist changes to disk
    try:
        # Only save dicts that were modified
        dicts_to_save = []

        if cleanup_results["pending_admin_reviews"]:
            dicts_to_save.append(("pending_admin_reviews", save_pending_admin_reviews))

        if cleanup_results["admin_remarks"]:
            dicts_to_save.append(("admin_remarks", save_admin_remarks))

        if cleanup_results["orders"]:
            dicts_to_save.append(("orders", save_current_orders))

        if cleanup_results["order_status"]:
            dicts_to_save.append(("order_status", save_order_status))

        if cleanup_results["awaiting_receipt"]:
            dicts_to_save.append(("awaiting_receipt", save_awaiting_receipt))

        if cleanup_results["current_order_numbers"]:
            dicts_to_save.append(("current_order_numbers", save_current_order_numbers))

        # Save each modified dictionary
        for dict_name, save_func in dicts_to_save:
            try:
                # Get the actual dictionary reference based on name
                dict_obj = globals()[dict_name]
                # Call the appropriate save function
                save_func(dict_obj)
                logger.debug(f"Successfully saved {dict_name} during cleanup")
            except Exception as save_error:
                logger.error(f"Error saving {dict_name} during cleanup: {save_error}")

        logger.info("Successfully saved all changes to disk during cleanup")
    except Exception as e:
        logger.error(f"Error saving changes to disk during cleanup: {e}")

    # Calculate success rate
    successful_ops = sum(1 for result in cleanup_results.values() if result)
    total_ops = len(cleanup_results)

    logger.info(
        f"Cleanup completed for order #{order_number}, user ID: {user_id}. {successful_ops}/{total_ops} operations succeeded."
    )
    return successful_ops > 0  # Return True if at least one operation succeeded


def get_order_history(user_id: int) -> List[Dict[str, Any]]:
    """Get order history for a user"""
    try:
        user_id_str = str(user_id)
        try:
            with open(ORDER_HISTORY_FILE, "r") as f:
                all_history = json.load(f)
                return all_history.get(user_id_str, [])
        except (FileNotFoundError, json.JSONDecodeError):
            return []
    except Exception as e:
        logger.error(f"Error getting order history: {e}")
        return []


def save_order_to_history(user_id: int, order_data: Dict[str, Any]) -> bool:
    """Save order to history for user"""
    try:
        # Convert user_id to string for JSON compatibility
        user_id_str = str(user_id)

        # Load current order history
        order_history = load_order_history()

        # Initialize user's order history if it doesn't exist
        if user_id_str not in order_history:
            order_history[user_id_str] = []

        # Add order to history with timestamp
        if "created_at" not in order_data:
            order_data["created_at"] = datetime.datetime.now().strftime(
                "%Y-%m-%d %H:%M:%S"
            )

        # Add username if available
        if "username" not in order_data and hasattr(order_data, "from_user"):
            order_data["username"] = order_data.from_user.username

        # Append order to history
        order_history[user_id_str].append(order_data)

        # Save updated history
        save_order_history(order_history)

        logger.info(f"Order saved to history for user {user_id}")
        return True
    except Exception as e:
        logger.error(f"Error saving order to history for user {user_id}: {e}")
        return False


def get_points_balance(user_id: int) -> int:
    """Get points balance for user - always fetches the latest data from Firebase"""
    try:
        user_id_str = str(user_id)

        if USE_FIREBASE:
            # Always fetch fresh data directly from Firebase
            # This bypasses any cached data and ensures we get the most recent values
            points_data = get_user_points(user_id_str)

            # Log the retrieved points for debugging
            if points_data and user_id_str in points_data:
                points_value = int(points_data[user_id_str])
                logger.debug(
                    f"Retrieved fresh points balance from Firebase for user {user_id}: {points_value}"
                )

                # Update the in-memory cache for consistency
                user_points[user_id_str] = points_value

                return points_value
            else:
                logger.debug(f"No points found in Firebase for user {user_id}")
                return 0
        else:
            # Legacy approach using JSON file
            # Always load the most current data directly from disk
            points_data = load_points()

            # Convert user_id to string for JSON compatibility
            if user_id_str in points_data:
                return int(points_data[user_id_str])
            else:
                # If user doesn't have points yet, return 0
                return 0
    except Exception as e:
        logger.error(f"Error getting points balance for user {user_id}: {e}")
        return 0


def update_points_balance(user_id: int, points_change: int) -> bool:
    """Update points balance for user - can be positive or negative change"""
    try:
        # Convert user_id to string for JSON compatibility
        user_id_str = str(user_id)

        if USE_FIREBASE:
            # Using Firebase
            # Get current balance from Firebase
            points_data = get_user_points(user_id_str)

            # Get current balance (defaults to 0 if not found)
            current_balance = int(points_data.get(user_id_str, 0)) if points_data else 0

            # Calculate new balance
            new_balance = current_balance + points_change

            # Ensure balance never goes below zero
            if new_balance < 0:
                new_balance = 0
                logger.warning(f"Tried to set negative balance for user {user_id}")

            # Update points in Firebase
            success = update_user_points(user_id_str, new_balance)

            # Also update in-memory dict
            user_points[user_id_str] = new_balance

            logger.info(
                f"Updated points for user {user_id}: {current_balance} -> {new_balance}"
            )
            return success
        else:
            # Legacy approach using JSON files
            # Load current points data directly from disk to ensure we have the latest
            points_data = load_points()

            # Get current balance (defaults to 0 if not found)
            current_balance = int(points_data.get(user_id_str, 0))

            # Calculate new balance
            new_balance = current_balance + points_change

            # Ensure balance never goes below zero
            if new_balance < 0:
                new_balance = 0
                logger.warning(f"Tried to set negative balance for user {user_id}")

            # Update points data and save
            points_data[user_id_str] = new_balance
            user_points[user_id_str] = new_balance  # Update in-memory dict too
            save_points(points_data)  # Ensure we save to disk immediately

            logger.info(
                f"Updated points for user {user_id}: {current_balance} -> {new_balance}"
            )
            return True
    except Exception as e:
        logger.error(f"Error updating points for user {user_id}: {e}")
        return False


def save_favorite_order(
    user_id: int, favorite_name: str, order_data: Dict[str, Any]
) -> bool:
    """Save a favorite order for a user"""
    try:
        user_id_str = str(user_id)

        # Initialize user's favorites if not exists
        if user_id_str not in favorite_orders:
            favorite_orders[user_id_str] = []

        # Make a copy and add the favorite name
        favorite = order_data.copy()
        favorite["favorite_name"] = favorite_name

        # Add the user's name to the favorite order data if available
        if user_id_str in user_names and user_names[user_id_str]:
            favorite["user_name"] = user_names[user_id_str]

        # Store current timestamp
        favorite["timestamp"] = datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")

        # Add to favorites
        favorite_orders[user_id_str].append(favorite)

        # Save to Firebase
        update_favorite_orders(user_id_str, favorite_orders[user_id_str])
        logger.info(f"Saved favorite order '{favorite_name}' for user {user_id}")
        return True
    except Exception as e:
        logger.error(f"Error saving favorite order: {e}")
        return False


def get_user_favorite_orders(user_id: int) -> List[Dict[str, Any]]:
    """Get list of favorite orders for a user from local memory"""
    try:
        user_id_str = str(user_id)
        return favorite_orders.get(user_id_str, [])
    except Exception as e:
        logger.error(f"Error getting user favorite orders: {e}")
        return []


def delete_favorite_order(user_id: int, index: int) -> bool:
    """Delete a favorite order by index"""
    try:
        user_id_str = str(user_id)
        if user_id_str in favorite_orders and 0 <= index < len(
            favorite_orders[user_id_str]
        ):
            # Remove the order at the specified index
            favorite_orders[user_id_str].pop(index)

            # Save to Firebase
            update_favorite_orders(user_id_str, favorite_orders[user_id_str])
            return True
        return False
    except Exception as e:
        logger.error(f"Error deleting favorite order: {e}")
        return False


def get_area_id_by_name(area_name):
    """Get area ID by name"""
    global areas_data

    # Check in dynamic areas data
    for area in areas_data.get("areas", []):
        if area["name"] == area_name:
            return area["id"]

    # If not found, generate a temporary ID
    # This is needed for compatibility with static config
    from src.config import restaurants as config_restaurants

    area_names = list(config_restaurants.keys())

    if area_name in area_names:
        # Use the position in the list + 1000 as a temporary ID
        # (to avoid conflicts with real IDs)
        return 1000 + area_names.index(area_name)

    return None


def set_current_order_area(user_id, area_name, area_id):
    """Store area information in user's current order"""
    user_id_str = str(user_id)

    # Initialize current order if needed
    if user_id_str not in orders:
        orders[user_id_str] = {}

    # Store area information
    orders[user_id_str]["area"] = area_name
    orders[user_id_str]["area_id"] = area_id

    # Set order status
    if user_id not in order_status:
        order_status[user_id] = "SELECTING_RESTAURANT"

    logger.info(f"Set order area for user {user_id} to {area_name} (ID: {area_id})")

    # Save to Firebase
    update_current_order(user_id_str, orders[user_id_str])
    update_order_status(user_id, order_status[user_id])

    return True
