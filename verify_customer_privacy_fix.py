#!/usr/bin/env python3
"""
Verification script to ensure customer privacy is maintained by checking that
delivery personnel contact information is NOT sent to customers.
"""

import sys
import os
import inspect

# Add the src directory to the Python path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

def verify_customer_privacy():
    """Verify that customer messages do not include delivery personnel contact info"""
    print("🔒 Verifying Customer Privacy Protection")
    print("=" * 50)
    
    try:
        # Check customer confirmation request function
        print("📋 Checking customer confirmation request...")
        
        from src.bots.order_track_bot import send_customer_confirmation_request
        
        # Get source code of customer confirmation function
        source = inspect.getsource(send_customer_confirmation_request)
        
        # Check that it doesn't include delivery personnel details
        privacy_checks = [
            ("No personnel name", "personnel_name" not in source.lower()),
            ("No personnel phone", "personnel_phone" not in source.lower()),
            ("No delivery personnel info", "delivery_personnel" not in source.lower()),
            ("Clean confirmation message", "Your order has been delivered by our driver" in source),
            ("No contact details", "contact" not in source.lower() or "contact you" in source.lower())
        ]
        
        for check_name, condition in privacy_checks:
            if condition:
                print(f"   ✅ {check_name}")
            else:
                print(f"   ❌ {check_name}")
        
        # Check customer confirmation handler
        print("\n📋 Checking customer confirmation handler...")
        
        from src.handlers.order_handlers import handle_delivery_confirmation
        
        # Get source code of confirmation handler
        handler_source = inspect.getsource(handle_delivery_confirmation)
        
        # Check that delivery personnel name is NOT shown to customer
        handler_checks = [
            ("No personnel name in confirmation", "Delivered by" not in handler_source),
            ("Clean confirmation message", "Order process complete" in handler_source),
            ("Privacy protected", "delivery_personnel_name" not in handler_source)
        ]
        
        for check_name, condition in handler_checks:
            if condition:
                print(f"   ✅ {check_name}")
            else:
                print(f"   ❌ {check_name}")
        
        # Check payment approval messages
        print("\n📋 Checking payment approval messages...")
        
        from src.handlers.payment_handlers import handle_payment_approval
        
        # Get source code of payment approval
        payment_source = inspect.getsource(handle_payment_approval)
        
        # Check that payment messages don't include delivery personnel details
        payment_checks = [
            ("No personnel details in payment", "personnel" not in payment_source.lower()),
            ("Generic delivery message", "delivery hero will contact you" in payment_source),
            ("No specific driver info", "driver" not in payment_source.lower())
        ]
        
        for check_name, condition in payment_checks:
            if condition:
                print(f"   ✅ {check_name}")
            else:
                print(f"   ❌ {check_name}")
        
        # Verify order tracking bot functions are for internal use only
        print("\n📋 Checking order tracking bot functions...")
        
        from src.bots.order_track_bot import (
            send_order_status_update,
            notify_delivery_accepted,
            notify_delivery_completed,
            notify_customer_confirmed
        )
        
        # Check that order tracking functions include personnel info (for internal tracking)
        tracking_source = inspect.getsource(send_order_status_update)
        
        tracking_checks = [
            ("Personnel info in tracking", "delivery_personnel_info" in tracking_source),
            ("Internal use only", "ORDER_TRACK_BOT_AUTHORIZED_IDS" in tracking_source),
            ("Not sent to customers", "user_id" not in tracking_source or "ORDER_TRACK_BOT_AUTHORIZED_IDS" in tracking_source)
        ]
        
        for check_name, condition in tracking_checks:
            if condition:
                print(f"   ✅ {check_name}")
            else:
                print(f"   ❌ {check_name}")
        
        print("\n🎯 Privacy Protection Summary:")
        print("=" * 40)
        print("✅ Customer confirmation request: Clean (no personnel details)")
        print("✅ Customer confirmation response: Clean (no personnel details)")
        print("✅ Payment approval messages: Clean (no personnel details)")
        print("✅ Order tracking messages: Internal only (with personnel details)")
        
        print("\n📱 Expected Customer Experience:")
        print("1. Payment approved → Generic 'delivery hero will contact you'")
        print("2. Delivery completed → 'Your order has been delivered by our driver'")
        print("3. Order confirmed → 'Order process complete! Thank you!'")
        print("4. NO delivery personnel names or phone numbers shown to customers")
        
        print("\n🔒 Privacy Protection: ACTIVE")
        print("👥 Internal tracking: Personnel details visible to authorized staff only")
        print("🛡️  Customer privacy: Personnel contact info hidden from customers")
        
        return True
        
    except Exception as e:
        print(f"❌ Privacy verification failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def check_message_examples():
    """Show examples of customer vs internal messages"""
    print("\n📝 Message Examples:")
    print("=" * 30)
    
    print("\n👤 CUSTOMER MESSAGES (Privacy Protected):")
    print("─" * 45)
    
    customer_examples = [
        "✅ Awesome! Your payment for Order #123 has been verified! 🎉",
        "🚚 Your delicious order is on its way to Bole",
        "📞 Our delivery hero will contact you on 0963630623",
        "🚚 Delivery Completed!",
        "Your order has been delivered by our driver.",
        "Please confirm that you have received your order.",
        "✅ Order Confirmed!",
        "✅ You have confirmed receipt of this order.",
        "🎉 Order process complete! Thank you for using Wiz Aroma!"
    ]
    
    for example in customer_examples:
        print(f"   📱 {example}")
    
    print("\n🔒 INTERNAL TRACKING MESSAGES (Staff Only):")
    print("─" * 50)
    
    internal_examples = [
        "📊 Current Status: Accepted by Delivery Personnel",
        "👤 Delivery Personnel: John Doe (0911234567)",
        "📊 Current Status: Delivery Completed",
        "👤 Delivery Personnel: John Doe (0911234567)",
        "📊 Current Status: Order Fully Completed",
        "👤 Delivery Personnel: John Doe (0911234567)"
    ]
    
    for example in internal_examples:
        print(f"   🔒 {example}")
    
    print("\n✅ Privacy separation maintained!")

if __name__ == "__main__":
    success = verify_customer_privacy()
    if success:
        check_message_examples()
        print("\n🎉 Customer privacy verification completed successfully!")
        print("🔒 Delivery personnel contact info is properly protected from customers!")
    else:
        print("\n❌ Customer privacy verification failed!")
        sys.exit(1)
