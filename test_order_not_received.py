#!/usr/bin/env python3
"""
Test script to verify Order Not Received callback handler functionality
"""

import sys
import os
sys.path.insert(0, 'src')

def test_callback_handler_registration():
    """Test if the callback handler is properly registered"""
    print("🔍 Testing callback handler registration...")

    try:
        from src.bot_instance import bot

        # Import handlers to ensure they are registered
        print("📥 Importing handlers...")
        import src.handlers.order_handlers  # This should register the callback handlers

        # Check registered callback handlers
        callback_handlers = bot.callback_query_handlers
        print(f"📋 Found {len(callback_handlers)} callback query handlers")
        
        # Look for our specific handler
        not_received_handler_found = False
        for i, handler in enumerate(callback_handlers):
            print(f"  Handler {i+1}: {handler}")
            # Test if this handler would match our callback data
            if hasattr(handler, 'func') and handler.func:
                try:
                    # Create a mock call object to test the filter
                    class MockCall:
                        def __init__(self, data):
                            self.data = data
                    
                    test_call = MockCall('not_received_12345_20241203_001')
                    if handler.func(test_call):
                        print(f"  ✅ Handler {i+1} matches 'not_received_' pattern")
                        not_received_handler_found = True
                except Exception as e:
                    print(f"  ❌ Error testing handler {i+1}: {e}")
        
        if not_received_handler_found:
            print("✅ Order Not Received callback handler is properly registered")
        else:
            print("❌ Order Not Received callback handler NOT found")
            
        return not_received_handler_found
        
    except Exception as e:
        print(f"❌ Error testing callback handler registration: {e}")
        return False

def test_delivery_personnel_lookup():
    """Test delivery personnel lookup functionality"""
    print("\n🔍 Testing delivery personnel lookup...")
    
    try:
        from src.utils.delivery_personnel_utils import get_delivery_personnel_by_id
        
        # Test with a known delivery personnel ID (you may need to adjust this)
        test_personnel_id = "5546595738"  # From the memories, this is a known delivery personnel ID
        
        personnel = get_delivery_personnel_by_id(test_personnel_id)
        
        if personnel:
            print(f"✅ Found personnel: {personnel.get('name', 'Unknown')}")
            print(f"   Telegram ID: {personnel.get('telegram_id', 'N/A')}")
            return True
        else:
            print(f"❌ No personnel found for ID: {test_personnel_id}")
            return False
            
    except Exception as e:
        print(f"❌ Error testing delivery personnel lookup: {e}")
        return False

def test_delivery_bot_connection():
    """Test delivery bot connection"""
    print("\n🔍 Testing delivery bot connection...")
    
    try:
        from src.bots.delivery_bot import delivery_bot
        
        bot_info = delivery_bot.get_me()
        print(f"✅ Delivery bot connected: @{bot_info.username}")
        return True
        
    except Exception as e:
        print(f"❌ Delivery bot connection failed: {e}")
        return False

def test_firebase_order_data():
    """Test Firebase order data access"""
    print("\n🔍 Testing Firebase order data access...")
    
    try:
        from src.firebase_db import get_data
        
        confirmed_orders = get_data("confirmed_orders") or {}
        print(f"📋 Found {len(confirmed_orders)} confirmed orders in Firebase")
        
        if confirmed_orders:
            # Show a sample order structure
            sample_order_number = list(confirmed_orders.keys())[0]
            sample_order = confirmed_orders[sample_order_number]
            print(f"📄 Sample order {sample_order_number}:")
            print(f"   - assigned_to: {sample_order.get('assigned_to', 'N/A')}")
            print(f"   - delivery_status: {sample_order.get('delivery_status', 'N/A')}")
            print(f"   - user_id: {sample_order.get('user_id', 'N/A')}")
            return True
        else:
            print("❌ No confirmed orders found in Firebase")
            return False
            
    except Exception as e:
        print(f"❌ Error accessing Firebase order data: {e}")
        return False

def main():
    """Run all tests"""
    print("🚀 Starting Order Not Received functionality tests...\n")
    
    tests = [
        ("Callback Handler Registration", test_callback_handler_registration),
        ("Delivery Personnel Lookup", test_delivery_personnel_lookup),
        ("Delivery Bot Connection", test_delivery_bot_connection),
        ("Firebase Order Data", test_firebase_order_data),
    ]
    
    results = []
    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ {test_name} failed with exception: {e}")
            results.append((test_name, False))
    
    print("\n" + "="*50)
    print("📊 TEST RESULTS SUMMARY")
    print("="*50)
    
    passed = 0
    for test_name, result in results:
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"{status} - {test_name}")
        if result:
            passed += 1
    
    print(f"\n🎯 {passed}/{len(results)} tests passed")
    
    if passed == len(results):
        print("🎉 All tests passed! Order Not Received functionality should work correctly.")
    else:
        print("⚠️  Some tests failed. Check the issues above before testing the actual workflow.")

if __name__ == "__main__":
    main()
