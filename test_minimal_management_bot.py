#!/usr/bin/env python3
"""
Minimal Management Bot Test
Tests the management bot without heavy data loading
"""

import sys
import os
import time

# Add the project root to the Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_minimal_management_bot():
    """Test management bot with minimal setup"""
    print("🧪 MINIMAL MANAGEMENT BOT TEST")
    print("=" * 40)
    
    try:
        # Import only the bot instance first
        from src.bot_instance import management_bot
        
        print("✅ Bot instance imported")
        
        # Test bot connection
        bot_info = management_bot.get_me()
        print(f"✅ Bot: @{bot_info.username}")
        
        # Create a simple handler without importing heavy modules
        @management_bot.message_handler(commands=['test'])
        def simple_test_handler(message):
            user_id = message.from_user.id
            if user_id == 7729984017:  # Authorized user
                management_bot.reply_to(message, "✅ Management bot is working! You are authorized.")
            else:
                management_bot.reply_to(message, "❌ Access denied. Unauthorized user.")
            print(f"📨 Received test command from user {user_id}")
        
        @management_bot.message_handler(commands=['start'])
        def simple_start_handler(message):
            user_id = message.from_user.id
            if user_id == 7729984017:  # Authorized user
                response = """🏢 **Management Bot Test**
                
✅ Bot is responding correctly!
✅ Authorization working
✅ Handlers registered

Send /test to verify functionality."""
                management_bot.reply_to(message, response, parse_mode='Markdown')
            else:
                management_bot.reply_to(message, "❌ Access denied.")
            print(f"📨 Received start command from user {user_id}")
        
        print("✅ Simple handlers registered")
        
        # Remove webhook
        try:
            management_bot.remove_webhook()
            print("✅ Webhook removed")
        except Exception as e:
            print(f"⚠️ Webhook: {e}")
        
        print("\n🚀 MANAGEMENT BOT IS READY!")
        print("=" * 40)
        print("Send these commands to @Wiz_Aroma_Finance_bot:")
        print("  /start - Test basic response")
        print("  /test  - Test authorization")
        print("\nPress Ctrl+C to stop...")
        print("=" * 40)
        
        # Start polling
        management_bot.infinity_polling(timeout=30, long_polling_timeout=30)
        
    except KeyboardInterrupt:
        print("\n⏹️ Bot stopped by user")
        return True
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    test_minimal_management_bot()
