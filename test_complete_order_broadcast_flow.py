#!/usr/bin/env python3
"""
Test the complete order broadcast flow after fixing service area mapping
"""

import sys
import os

# Add the project root to the Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_order_broadcast_for_area_2():
    """Test order broadcast specifically for area 2 (the failing case)"""
    print("📡 TESTING ORDER BROADCAST FOR AREA 2")
    print("=" * 60)
    
    try:
        from src.utils.delivery_personnel_utils import find_available_personnel_with_capacity_check
        
        # Test the exact scenario that was failing
        area_id = '2'  # Geda Gate Area - this was failing before
        
        print(f"🎯 Testing order broadcast for area '{area_id}' (Geda Gate Area)")
        print(f"   This was the failing case from the logs")
        
        # Find available personnel with capacity check (like the real system does)
        available_personnel_ids = find_available_personnel_with_capacity_check(area_id)
        
        print(f"📊 Found {len(available_personnel_ids)} available personnel for area {area_id}")
        
        if available_personnel_ids:
            print(f"✅ SUCCESS: Personnel available for area {area_id}")
            print(f"   Available personnel: {available_personnel_ids}")
            
            # Get personnel details
            from src.firebase_db import get_data
            personnel_data = get_data("delivery_personnel") or {}
            
            print(f"\n👥 Personnel Details:")
            for pid in available_personnel_ids:
                pdata = personnel_data.get(pid, {})
                name = pdata.get('name', 'Unknown')
                service_areas = pdata.get('service_areas', [])
                print(f"   👤 {name} ({pid}): serves areas {service_areas}")
            
            return True
        else:
            print(f"❌ FAILED: No personnel available for area {area_id}")
            print(f"   This indicates the fix didn't work properly")
            return False
        
    except Exception as e:
        print(f"❌ Error testing order broadcast: {e}")
        import traceback
        traceback.print_exc()
        return False

def simulate_complete_order_flow():
    """Simulate the complete order flow from approval to broadcast"""
    print("\n🔄 SIMULATING COMPLETE ORDER FLOW")
    print("=" * 60)
    
    try:
        from src.firebase_db import get_data, set_data
        import datetime
        
        # Create a test order for area 2 (the failing case)
        test_order_number = f"TEST_{datetime.datetime.now().strftime('%Y%m%d_%H%M%S')}"
        
        test_order = {
            "order_number": test_order_number,
            "customer_name": "Test Customer",
            "customer_phone": "+251911223344",
            "restaurant_name": "Test Restaurant",
            "delivery_address": "Test Address, Geda Gate Area",
            "order_items": [
                {"name": "Test Item", "price": 50.0, "quantity": 1}
            ],
            "subtotal": 50.0,
            "delivery_fee": 25.0,
            "total": 75.0,
            "area_id": "2",  # Geda Gate Area - the failing case
            "restaurant_area_id": "2",
            "status": "approved",
            "created_at": datetime.datetime.now().isoformat()
        }
        
        print(f"📋 Created test order:")
        print(f"   Order Number: {test_order_number}")
        print(f"   Area ID: {test_order['area_id']} (Geda Gate Area)")
        print(f"   Total: {test_order['total']} birr")
        
        # Save test order to Firebase
        success = set_data(f"orders/{test_order_number}", test_order)
        if not success:
            print(f"❌ Failed to save test order")
            return False
        
        print(f"✅ Test order saved to Firebase")
        
        # Test the delivery broadcast logic
        print(f"\n📡 Testing delivery broadcast logic...")
        
        delivery_area_id = test_order.get('restaurant_area_id') or test_order.get('area_id')
        print(f"   Delivery area ID: {delivery_area_id}")
        
        # Find available personnel (like the real system does)
        from src.utils.delivery_personnel_utils import find_available_personnel_with_capacity_check
        available_personnel_ids = find_available_personnel_with_capacity_check(str(delivery_area_id))
        
        print(f"   Found {len(available_personnel_ids)} available personnel")
        
        if available_personnel_ids:
            print(f"✅ SUCCESS: Order broadcast would work!")
            print(f"   Personnel would receive notification: {available_personnel_ids}")
            
            # Clean up test order
            set_data(f"orders/{test_order_number}", None)
            print(f"🧹 Cleaned up test order")
            
            return True
        else:
            print(f"❌ FAILED: No available personnel for order broadcast")
            return False
        
    except Exception as e:
        print(f"❌ Error simulating order flow: {e}")
        import traceback
        traceback.print_exc()
        return False

def verify_all_areas_work():
    """Verify that all areas now work for order broadcasts"""
    print("\n🌍 VERIFYING ALL AREAS WORK")
    print("=" * 60)
    
    try:
        from src.utils.delivery_personnel_utils import find_available_personnel_with_capacity_check
        
        # Test all areas
        areas = {
            '1': 'Bole Area',
            '2': 'Geda Gate Area',
            '3': 'Kereyu Area', 
            '4': 'College Mecheresha Area',
            '5': 'Stadium Area'
        }
        
        all_areas_work = True
        
        for area_id, area_name in areas.items():
            print(f"\n🎯 Testing area '{area_id}' ({area_name}):")
            
            available_personnel = find_available_personnel_with_capacity_check(area_id)
            
            if available_personnel:
                print(f"   ✅ {len(available_personnel)} personnel available")
            else:
                print(f"   ❌ No personnel available")
                all_areas_work = False
        
        if all_areas_work:
            print(f"\n🎉 SUCCESS: All areas have available personnel!")
        else:
            print(f"\n⚠️ WARNING: Some areas have no available personnel")
        
        return all_areas_work
        
    except Exception as e:
        print(f"❌ Error verifying all areas: {e}")
        return False

def test_management_bot_new_personnel():
    """Test that new personnel added via management bot will have correct areas"""
    print("\n🤖 TESTING MANAGEMENT BOT NEW PERSONNEL")
    print("=" * 60)
    
    try:
        # Simulate adding a new personnel via management bot
        from src.data_models import DeliveryPersonnel
        import uuid
        
        # Create a test personnel like management bot does
        personnel_id = f"dp_{uuid.uuid4().hex[:8]}"
        
        personnel = DeliveryPersonnel(personnel_id)
        personnel.name = "Test New Personnel"
        personnel.phone_number = "+************"
        personnel.telegram_id = "9999888777"
        personnel.service_areas = ['1', '2', '3', '4', '5']  # New default areas
        personnel.vehicle_type = 'motorcycle'
        personnel.max_capacity = 5
        personnel.status = 'available'
        personnel.is_verified = True
        
        print(f"👤 Created test personnel: {personnel.name}")
        print(f"   Service areas: {personnel.service_areas}")
        
        # Test area matching
        test_areas = ['1', '2', '3', '4', '5']
        all_areas_match = True
        
        for area in test_areas:
            can_serve = personnel.can_serve_area(area)
            status = "✅" if can_serve else "❌"
            print(f"   {status} Can serve area '{area}': {can_serve}")
            
            if not can_serve:
                all_areas_match = False
        
        if all_areas_match:
            print(f"✅ SUCCESS: New personnel can serve all areas")
        else:
            print(f"❌ FAILED: New personnel cannot serve some areas")
        
        return all_areas_match
        
    except Exception as e:
        print(f"❌ Error testing management bot new personnel: {e}")
        return False

def main():
    """Run complete order broadcast flow test"""
    print("🚀 COMPLETE ORDER BROADCAST FLOW TEST")
    print("=" * 70)
    print("Testing the fix for service area mapping mismatch")
    print("=" * 70)
    
    tests = [
        ("Test Order Broadcast for Area 2", test_order_broadcast_for_area_2),
        ("Simulate Complete Order Flow", simulate_complete_order_flow),
        ("Verify All Areas Work", verify_all_areas_work),
        ("Test Management Bot New Personnel", test_management_bot_new_personnel)
    ]
    
    results = []
    for test_name, test_func in tests:
        try:
            print(f"\n🔄 RUNNING: {test_name}")
            result = test_func()
            results.append(result)
            status = "✅ PASSED" if result else "❌ FAILED"
            print(f"\n{status}: {test_name}")
        except Exception as e:
            print(f"\n❌ ERROR in {test_name}: {e}")
            results.append(False)
    
    print("\n" + "=" * 70)
    print("📊 TEST SUMMARY")
    print("=" * 70)
    
    passed = sum(results)
    total = len(results)
    
    for i, (test_name, _) in enumerate(tests):
        status = "✅ PASSED" if results[i] else "❌ FAILED"
        print(f"{status}: {test_name}")
    
    print(f"\nOverall: {passed}/{total} tests passed")
    
    if all(results):
        print("\n🎉 ALL TESTS PASSED!")
        print("✅ Service area mapping issue is COMPLETELY FIXED")
        print("✅ Order broadcasts will work for all areas")
        print("✅ Delivery personnel will receive notifications")
        print("✅ Management bot will add personnel with correct areas")
        print("\n📋 READY FOR PRODUCTION:")
        print("   • Orders in area 2 (Geda Gate) will now broadcast correctly")
        print("   • All 5 delivery personnel can receive orders")
        print("   • New personnel added via management bot will work correctly")
        print("   • Complete order workflow is functional")
        return True
    else:
        print("\n⚠️ SOME TESTS FAILED")
        print("Please review the issues above")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
