# 📡 Data Management System API Documentation

## Overview

This document provides technical documentation for the Data Management System API endpoints, functions, and security requirements implemented in the Wiz-Aroma management bot.

## 🔐 Security and Authorization

### Access Control
```python
AUTHORIZED_MANAGEMENT_IDS = [
    "7729984017",  # Primary admin
    # Add additional authorized user IDs here
]

def is_authorized_for_reset(user_id):
    """Check if user is authorized for reset operations"""
    return str(user_id) in AUTHORIZED_MANAGEMENT_IDS
```

### Session Management
- **Confirmation State**: Temporary session storage for multi-step operations
- **Timeout Protection**: Automatic session cleanup
- **User Validation**: Continuous authorization checking

## 🔄 Core API Functions

### Firebase Operations Module

#### `log_reset_operation(operation_type, user_id, details=None)`
**Purpose**: Log reset operations for audit purposes with enhanced error handling

**Parameters**:
- `operation_type` (str): Type of operation (seasonal_reset, daily_cleanup_standard, etc.)
- `user_id` (str): Telegram user ID performing the operation
- `details` (dict, optional): Additional operation details

**Returns**: `str` - Log ID for tracking, or fallback ID on error

**Error Handling**: Safe timestamp generation with fallback mechanisms

#### `create_data_backup(collections_to_backup, backup_name)`
**Purpose**: Create timestamped backup of specified collections

**Parameters**:
- `collections_to_backup` (list): List of Firebase collection names
- `backup_name` (str): Descriptive name for the backup

**Returns**: `tuple` - (backup_id, total_records) or (None, 0) on error

**Collections Backed Up**:
- `completed_orders`
- `confirmed_orders`
- `delivery_personnel_assignments`
- `delivery_personnel_earnings`

#### `batch_clear_collections(collections_to_clear, preserve_current_day=False)`
**Purpose**: Clear specified collections using batch operations

**Parameters**:
- `collections_to_clear` (list): Collections to clear
- `preserve_current_day` (bool): Whether to preserve current day data

**Returns**: `dict` - Count of cleared records per collection

#### `reset_personnel_earnings_only()`
**Purpose**: Reset only earnings data while preserving personnel profiles

**Returns**: `int` - Number of personnel earnings records reset

#### `archive_incomplete_orders(hours_threshold=24)`
**Purpose**: Archive orders that are incomplete beyond threshold

**Parameters**:
- `hours_threshold` (int): Hours after which orders are considered stale

**Returns**: `int` - Number of orders archived

### Seasonal Reset Functions

#### `show_system_management_menu(call)`
**Purpose**: Display system management menu with reset options

**Authorization**: Required - checks `is_authorized_for_reset()`

**Menu Options**:
- Seasonal Reset
- Daily Cleanup
- System Status
- Audit Log

#### `initiate_seasonal_reset(call)`
**Purpose**: Start seasonal reset process with comprehensive warning

**Flow**:
1. Authorization check
2. Data summary generation
3. Warning dialog display
4. Confirmation requirement setup

#### `confirm_seasonal_reset(call)`
**Purpose**: Request typed confirmation for seasonal reset

**Confirmation Phrase**: `"CONFIRM SEASON RESET"` (exact match required)

**State Management**: Uses `reset_confirmation_state` dictionary

#### `execute_seasonal_reset(message, user_id)`
**Purpose**: Execute the actual seasonal reset operation

**Steps**:
1. Create backup archive
2. Clear analytics collections
3. Reset personnel earnings
4. Update audit log
5. Send completion report

### Daily Cleanup Functions

#### `initiate_daily_cleanup(call)`
**Purpose**: Show daily cleanup options with current system analysis

**Analysis Includes**:
- Stale orders count (>24h)
- Abandoned orders count (>2h)
- Failed assignments count
- Cleanup recommendations

#### `execute_standard_cleanup(call)`
**Purpose**: Execute 24-hour cleanup operation

**Operations**:
- Archive orders older than 24 hours
- Remove failed assignments
- Update personnel availability
- Generate completion report

#### `execute_quick_cleanup(call)`
**Purpose**: Execute 2-hour aggressive cleanup

**Operations**:
- Archive orders older than 2 hours
- Clean old assignments
- Free up personnel
- Optimize for new orders

### Security and Monitoring Functions

#### `show_system_status(call)`
**Purpose**: Display comprehensive system health dashboard

**Metrics**:
- System health status
- Order statistics
- Personnel availability
- Stale order analysis
- Performance recommendations

#### `show_audit_log(call)`
**Purpose**: Display recent system operations log

**Features**:
- Last 10 operations
- Operation status indicators
- User identification
- Timestamp formatting

#### `show_order_status(call)`
**Purpose**: Detailed order status analysis

**Analysis**:
- Today's activity summary
- Order age breakdown
- Assignment status
- Cleanup recommendations

## 🛡️ Error Handling and Safety

### Enhanced DateTime Handling
```python
def safe_get_current_timestamp():
    """Safely get current timestamp with fallback"""
    try:
        return datetime.now().isoformat()
    except Exception as e:
        logger.error(f"Error getting current timestamp: {e}")
        import time
        return str(int(time.time()))

def safe_parse_datetime(date_string, fallback_format='%Y-%m-%d'):
    """Safely parse datetime string with fallback"""
    try:
        if 'T' in date_string or 'Z' in date_string:
            return datetime.fromisoformat(date_string.replace('Z', '+00:00'))
        else:
            return datetime.strptime(date_string[:10], fallback_format)
    except Exception as e:
        logger.warning(f"Error parsing datetime '{date_string}': {e}")
        return datetime.now()
```

### Fallback Mechanisms
- **Timestamp Generation**: Falls back to Unix timestamp on datetime errors
- **Data Parsing**: Uses current time as fallback for invalid dates
- **Operation Logging**: Creates fallback log IDs on Firebase errors
- **Backup Creation**: Continues with basic timestamp on formatting errors

## 📊 Data Preservation Logic

### Collections Cleared in Seasonal Reset
```python
collections_to_clear = [
    "completed_orders",
    "delivery_personnel_assignments"
]
```

### Collections Preserved
```python
preserved_collections = [
    "delivery_personnel",           # Personnel profiles
    "areas",                       # Geographic areas
    "cafes",                       # Restaurant data
    "menus",                       # Menu items
    "delivery_locations",          # Delivery addresses
    "user_preferences",            # Customer settings
    "system_configuration"         # Bot settings
]
```

### Current Day Protection
- Orders from current day are preserved during cleanup
- Uses date comparison: `confirmed_at.startswith(today)`
- Prevents accidental deletion of active orders

## 🔄 Callback Handler Integration

### Menu Integration
```python
# Main menu keyboard includes System Management
keyboard.add(
    types.InlineKeyboardButton("🔧 System Management", callback_data="system_management")
)
```

### Callback Handlers
```python
# System Management callbacks
elif call.data == "system_management":
    show_system_management_menu(call)
elif call.data == "reset_season_init":
    initiate_seasonal_reset(call)
elif call.data == "reset_season_confirm":
    confirm_seasonal_reset(call)
elif call.data == "reset_daily_init":
    initiate_daily_cleanup(call)
elif call.data == "cleanup_standard":
    execute_standard_cleanup(call)
elif call.data == "cleanup_quick":
    execute_quick_cleanup(call)
```

## 📈 Analytics Integration

### Zero-Data Scenario Handling
```python
# Check for recent seasonal reset
recent_reset = None
for log_id, entry in audit_data.items():
    if entry.get('operation_type') == 'seasonal_reset' and entry.get('status') == 'completed':
        try:
            reset_time = datetime.fromisoformat(entry.get('completed_at', ''))
            if (datetime.now() - reset_time).days < 7:
                recent_reset = reset_time.strftime('%Y-%m-%d')
                break
        except:
            continue
```

### Analytics Display States
1. **Fresh Reset State**: Shows "New Season Started" message
2. **Zero Data State**: Shows "No completed orders found" message  
3. **Normal State**: Shows standard analytics with data

## 🚀 Usage Examples

### Starting Management Bot
```bash
python main.py --bot management
```

### Accessing System Management
```
Management Bot → Main Menu → 🔧 System Management
```

### Performing Seasonal Reset
```
System Management → 🔄 Reset Season Data → 
Review Warning → Confirm Understanding → 
Type "CONFIRM SEASON RESET" → Monitor Progress
```

### Daily Cleanup
```
System Management → 🧹 Daily Cleanup → 
Choose Standard/Quick → Execute → View Results
```

---

**⚠️ Security Note**: All operations require proper authorization and are logged for audit purposes. Unauthorized access attempts are blocked and logged.
