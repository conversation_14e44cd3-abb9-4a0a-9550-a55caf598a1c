#!/usr/bin/env python3
"""
Test script to verify the enhanced order notification system.
Tests all four main enhancements:
1. Delivery bot order details enhancement
2. User bot notification consolidation
3. Order not received functionality
4. Message editing patterns
"""

import sys
import os
import inspect
import re

# Add the project root to the Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def test_delivery_bot_enhancements():
    """Test delivery bot order details enhancement"""
    print("\n🚚 TESTING DELIVERY BOT ENHANCEMENTS")
    print("=" * 50)
    
    try:
        # Check delivery bot file for enhanced order acceptance message
        with open('src/bots/delivery_bot.py', 'r') as f:
            content = f.read()
            
        # Test 1: Enhanced acceptance message
        acceptance_patterns = [
            r'ORDER.*ACCEPTED.*Order Details',
            r'Restaurant:.*restaurant_name',
            r'Delivery to:.*delivery_gate',
            r'Customer Phone:.*phone_number',
            r'Items:.*items_text',
            r'Total Amount:.*total_amount.*birr',
            r'Accepted at:.*assigned_at'
        ]
        
        acceptance_found = all(re.search(pattern, content, re.IGNORECASE | re.DOTALL) for pattern in acceptance_patterns)
        
        if acceptance_found:
            print("✅ Enhanced order acceptance message implemented")
        else:
            print("❌ Enhanced order acceptance message missing")
            return False
            
        # Test 2: Enhanced completion message
        completion_patterns = [
            r'ORDER.*COMPLETED.*Order Details',
            r'Items Delivered:.*items_text',
            r'Delivered to:.*delivery_gate',
            r'Completed at:.*completed_at'
        ]
        
        completion_found = all(re.search(pattern, content, re.IGNORECASE | re.DOTALL) for pattern in completion_patterns)
        
        if completion_found:
            print("✅ Enhanced order completion message implemented")
        else:
            print("❌ Enhanced order completion message missing")
            return False
            
        return True
        
    except Exception as e:
        print(f"❌ Error testing delivery bot enhancements: {e}")
        return False

def test_user_bot_consolidation():
    """Test user bot notification consolidation"""
    print("\n👤 TESTING USER BOT NOTIFICATION CONSOLIDATION")
    print("=" * 50)
    
    try:
        # Test 1: Payment message ID storage
        with open('src/handlers/payment_handlers.py', 'r') as f:
            payment_content = f.read()
            
        storage_patterns = [
            r'user_payment_messages.*order_number',
            r'message_id.*sent_message\.message_id',
            r'chat_id.*sent_message\.chat\.id'
        ]
        
        storage_found = all(re.search(pattern, payment_content, re.IGNORECASE | re.DOTALL) for pattern in storage_patterns)
        
        if storage_found:
            print("✅ Payment message ID storage implemented")
        else:
            print("❌ Payment message ID storage missing")
            return False
            
        # Test 2: Message editing in order tracking bot
        with open('src/bots/order_track_bot.py', 'r') as f:
            tracking_content = f.read()
            
        editing_patterns = [
            r'get_data.*user_payment_messages',
            r'edit_message_text.*merged_message',
            r'Payment Verified.*Order Delivered',
            r'Thank you for choosing Wiz Aroma'
        ]
        
        editing_found = all(re.search(pattern, tracking_content, re.IGNORECASE | re.DOTALL) for pattern in editing_patterns)
        
        if editing_found:
            print("✅ Message editing consolidation implemented")
        else:
            print("❌ Message editing consolidation missing")
            return False
            
        return True
        
    except Exception as e:
        print(f"❌ Error testing user bot consolidation: {e}")
        return False

def test_order_not_received_functionality():
    """Test order not received functionality"""
    print("\n🚨 TESTING ORDER NOT RECEIVED FUNCTIONALITY")
    print("=" * 50)
    
    try:
        # Test 1: Order not received button in tracking bot
        with open('src/bots/order_track_bot.py', 'r') as f:
            tracking_content = f.read()
            
        button_patterns = [
            r'Order Not Received',
            r'not_received_.*order_number',
            r'markup\.row.*confirm_btn.*not_received_btn'
        ]
        
        button_found = all(re.search(pattern, tracking_content, re.IGNORECASE | re.DOTALL) for pattern in button_patterns)
        
        if button_found:
            print("✅ Order Not Received button implemented")
        else:
            print("❌ Order Not Received button missing")
            return False
            
        # Test 2: Order not received handler
        with open('src/handlers/order_handlers.py', 'r') as f:
            handler_content = f.read()
            
        handler_patterns = [
            r'handle_order_not_received',
            r'not_received_.*order_number',
            r'delivery_status.*delivery_issue',
            r'Complete Order.*button.*reappear'
        ]
        
        handler_found = all(re.search(pattern, handler_content, re.IGNORECASE | re.DOTALL) for pattern in handler_patterns)
        
        if handler_found:
            print("✅ Order Not Received handler implemented")
        else:
            print("❌ Order Not Received handler missing")
            return False
            
        return True
        
    except Exception as e:
        print(f"❌ Error testing order not received functionality: {e}")
        return False

def test_message_editing_patterns():
    """Test consistent message editing patterns"""
    print("\n📝 TESTING MESSAGE EDITING PATTERNS")
    print("=" * 50)
    
    try:
        # Test error handling patterns across all bots
        files_to_check = [
            'src/bots/delivery_bot.py',
            'src/bots/order_track_bot.py',
            'src/handlers/order_handlers.py'
        ]
        
        for file_path in files_to_check:
            with open(file_path, 'r') as f:
                content = f.read()
                
            # Check for proper error handling in message editing
            error_patterns = [
                r'edit_message_text',
                r'except.*edit.*error',
                r'logger\.error.*edit'
            ]
            
            error_handling = any(re.search(pattern, content, re.IGNORECASE | re.DOTALL) for pattern in error_patterns)
            
            if error_handling:
                print(f"✅ {file_path}: Message editing with error handling")
            else:
                print(f"⚠️  {file_path}: Limited message editing patterns")
                
        return True
        
    except Exception as e:
        print(f"❌ Error testing message editing patterns: {e}")
        return False

def main():
    """Run all enhancement tests"""
    print("🧪 TESTING ENHANCED ORDER NOTIFICATION SYSTEM")
    print("=" * 60)
    
    tests = [
        ("Delivery Bot Enhancements", test_delivery_bot_enhancements),
        ("User Bot Consolidation", test_user_bot_consolidation),
        ("Order Not Received Functionality", test_order_not_received_functionality),
        ("Message Editing Patterns", test_message_editing_patterns)
    ]
    
    results = []
    
    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"❌ {test_name} failed with error: {e}")
            results.append((test_name, False))
    
    # Summary
    print("\n📊 ENHANCEMENT TEST SUMMARY")
    print("=" * 40)
    
    passed = sum(1 for _, result in results if result)
    total = len(results)
    
    for test_name, result in results:
        status = "✅ PASSED" if result else "❌ FAILED"
        print(f"{status}: {test_name}")
    
    print(f"\n🎯 Overall Result: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All enhancements successfully implemented!")
        return True
    else:
        print("⚠️  Some enhancements need attention")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
