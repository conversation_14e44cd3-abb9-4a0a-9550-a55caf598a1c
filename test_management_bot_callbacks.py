#!/usr/bin/env python3
"""
Test Management Bot Callback Functionality
Tests inline keyboard button callbacks and responses
"""

import sys
import os
import time

# Add the project root to the Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_management_bot_callbacks():
    """Test Management Bot callback query handling"""
    print("🧪 TESTING MANAGEMENT BOT CALLBACKS")
    print("=" * 60)
    
    try:
        # Import required modules
        from src.bots.management_bot import management_bot, handle_callback_query
        from telebot.types import CallbackQuery, User, Chat, Message
        
        print("✅ Management bot modules imported successfully")
        
        # Test bot connection
        bot_info = management_bot.get_me()
        print(f"✅ Bot connected: @{bot_info.username}")
        
        # Create mock authorized user
        mock_user = User(
            id=7729984017,
            is_bot=False,
            first_name="Test",
            username="testuser",
            last_name="User",
            language_code="en"
        )
        
        # Create mock chat
        mock_chat = Chat(
            id=7729984017,
            type="private"
        )
        
        # Create mock message (for callback query)
        mock_message = Message(
            message_id=1,
            from_user=mock_user,
            date=int(time.time()),
            chat=mock_chat,
            content_type="text",
            options={},
            json_string=""
        )
        
        # Capture bot responses
        responses = []
        callback_answers = []
        
        # Mock bot methods
        original_answer_callback_query = management_bot.answer_callback_query
        original_edit_message_text = management_bot.edit_message_text
        original_send_message = management_bot.send_message
        
        def mock_answer_callback_query(callback_query_id, text=None, show_alert=False, **kwargs):
            callback_answers.append({
                'callback_query_id': callback_query_id,
                'text': text,
                'show_alert': show_alert,
                'kwargs': kwargs
            })
            print(f"📞 Callback answered: {text or 'No text'} (Alert: {show_alert})")
            return True
        
        def mock_edit_message_text(text, chat_id, message_id, **kwargs):
            responses.append({
                'type': 'edit_message',
                'text': text,
                'chat_id': chat_id,
                'message_id': message_id,
                'kwargs': kwargs
            })
            print(f"✏️ Message edited: {text[:50]}...")
            return type('MockMessage', (), {'message_id': message_id})()
        
        def mock_send_message(chat_id, text, **kwargs):
            responses.append({
                'type': 'send_message',
                'chat_id': chat_id,
                'text': text,
                'kwargs': kwargs
            })
            print(f"📤 Message sent: {text[:50]}...")
            return type('MockMessage', (), {'message_id': len(responses)})()
        
        # Replace bot methods with mocks
        management_bot.answer_callback_query = mock_answer_callback_query
        management_bot.edit_message_text = mock_edit_message_text
        management_bot.send_message = mock_send_message
        
        # Test each main menu button
        test_buttons = [
            ("mgmt_personnel", "👥 Personnel Management"),
            ("mgmt_analytics", "📊 Analytics Dashboard"),
            ("mgmt_reports", "📈 Reports"),
            ("mgmt_earnings", "💰 Earnings"),
            ("mgmt_refresh", "🔄 Refresh Data"),
            ("mgmt_info", "ℹ️ System Info")
        ]
        
        print(f"\n🔘 Testing {len(test_buttons)} main menu buttons...")
        
        for callback_data, button_name in test_buttons:
            print(f"\n--- Testing: {button_name} ---")
            
            # Clear previous responses
            responses.clear()
            callback_answers.clear()
            
            # Create mock callback query
            mock_callback = CallbackQuery(
                id=f"test_{callback_data}",
                from_user=mock_user,
                message=mock_message,
                data=callback_data,
                chat_instance="test_chat",
                json_string=""
            )
            
            try:
                # Test the callback handler
                handle_callback_query(mock_callback)
                
                # Check if callback was answered
                if callback_answers:
                    print(f"✅ Callback answered: {len(callback_answers)} response(s)")
                    for answer in callback_answers:
                        if answer['text']:
                            print(f"   Text: {answer['text']}")
                        print(f"   Alert: {answer['show_alert']}")
                else:
                    print("⚠️ No callback answer received")
                
                # Check if message was sent/edited
                if responses:
                    print(f"✅ Bot response: {len(responses)} message(s)")
                    for response in responses:
                        print(f"   Type: {response['type']}")
                        print(f"   Text: {response['text'][:100]}...")
                        if 'reply_markup' in response['kwargs']:
                            markup = response['kwargs']['reply_markup']
                            if hasattr(markup, 'keyboard'):
                                print(f"   Keyboard: {len(markup.keyboard)} rows")
                else:
                    print("⚠️ No message response received")
                    
            except Exception as e:
                print(f"❌ Error testing {button_name}: {e}")
                import traceback
                traceback.print_exc()
        
        # Test unauthorized user
        print(f"\n🚫 Testing unauthorized user...")
        
        mock_unauthorized_user = User(
            id=12345678,
            is_bot=False,
            first_name="Unauthorized",
            username="unauthorized",
            last_name="User",
            language_code="en"
        )
        
        mock_unauthorized_callback = CallbackQuery(
            id="test_unauthorized",
            from_user=mock_unauthorized_user,
            message=mock_message,
            data="mgmt_personnel",
            chat_instance="test_chat",
            json_string=""
        )
        
        responses.clear()
        callback_answers.clear()
        
        try:
            handle_callback_query(mock_unauthorized_callback)
            
            if callback_answers:
                answer = callback_answers[0]
                if "Access denied" in answer['text'] and answer['show_alert']:
                    print("✅ Unauthorized user properly blocked")
                else:
                    print(f"⚠️ Unexpected unauthorized response: {answer['text']}")
            else:
                print("❌ No response to unauthorized user")
                
        except Exception as e:
            print(f"❌ Error testing unauthorized user: {e}")
        
        # Restore original methods
        management_bot.answer_callback_query = original_answer_callback_query
        management_bot.edit_message_text = original_edit_message_text
        management_bot.send_message = original_send_message
        
        print("\n" + "=" * 60)
        print("🎉 CALLBACK TESTING COMPLETE")
        print("=" * 60)
        
        return True
        
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = test_management_bot_callbacks()
    if success:
        print("\n🎉 Callback test completed!")
    else:
        print("\n💥 Callback test failed!")
        sys.exit(1)
