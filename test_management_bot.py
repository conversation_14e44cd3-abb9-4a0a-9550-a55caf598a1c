#!/usr/bin/env python3
"""
Simple test script for the Management Bot
"""

import sys
import os

# Add the project root to Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

try:
    print("Testing Management Bot imports...")
    
    # Test basic imports
    import telebot
    print("✅ telebot imported successfully")
    
    from src.config import MANAGEMENT_BOT_TOKEN, logger
    print("✅ config imported successfully")
    print(f"✅ Token available: {bool(MANAGEMENT_BOT_TOKEN)}")
    
    # Test management bot import
    from src.bots.management_bot import management_bot, is_authorized_user
    print("✅ management_bot imported successfully")
    
    # Test bot connection
    bot_info = management_bot.get_me()
    print(f"✅ Bot connected: @{bot_info.username}")
    
    # Test authorization function
    test_result = is_authorized_user(7729984017)
    print(f"✅ Authorization test: {test_result}")
    
    print("\n🎉 All tests passed! Management Bot is ready to use.")
    print("\nTo start the Management Bot, run:")
    print("python main.py --bot management")
    
except Exception as e:
    print(f"❌ Error: {e}")
    import traceback
    traceback.print_exc()
    sys.exit(1)
