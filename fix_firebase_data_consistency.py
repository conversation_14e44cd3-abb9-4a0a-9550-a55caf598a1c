#!/usr/bin/env python3
"""
Data consistency fix script for Wiz-Aroma system.
This script helps resolve ID mismatches and data inconsistencies in Firebase.
"""

import sys
import os

# Add the src directory to the Python path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

def analyze_current_data():
    """Analyze current Firebase data to understand the inconsistencies"""
    print("🔍 Analyzing current Firebase data...")
    
    try:
        from src.firebase_db import get_data
        
        # Load all data
        areas_data = get_data("areas") or {"areas": []}
        restaurants_data = get_data("restaurants") or {"restaurants": []}
        delivery_locations_data = get_data("delivery_locations") or {"delivery_locations": []}
        delivery_fees_data = get_data("delivery_fees") or {"delivery_fees": []}
        
        areas = areas_data.get("areas", [])
        restaurants = restaurants_data.get("restaurants", [])
        delivery_locations = delivery_locations_data.get("delivery_locations", [])
        delivery_fees = delivery_fees_data.get("delivery_fees", [])
        
        print(f"\n📊 Current Data Summary:")
        print(f"  Areas: {len(areas)}")
        print(f"  Restaurants: {len(restaurants)}")
        print(f"  Delivery Locations: {len(delivery_locations)}")
        print(f"  Delivery Fees: {len(delivery_fees)}")
        
        # Show area IDs
        if areas:
            area_ids = [area.get("id") for area in areas]
            print(f"\n🏢 Area IDs: {area_ids}")
            for area in areas:
                print(f"  - {area.get('name')} (ID: {area.get('id')})")
        
        # Show delivery location IDs
        if delivery_locations:
            location_ids = [loc.get("id") for loc in delivery_locations]
            print(f"\n📍 Delivery Location IDs: {location_ids}")
            for location in delivery_locations:
                print(f"  - {location.get('name')} (ID: {location.get('id')})")
        
        # Show delivery fee mappings
        if delivery_fees:
            print(f"\n💰 Delivery Fee Mappings:")
            for fee in delivery_fees:
                area_id = fee.get("area_id")
                location_id = fee.get("location_id")
                fee_amount = fee.get("fee")
                print(f"  - Area {area_id} -> Location {location_id}: {fee_amount} birr")
        
        return {
            "areas": areas,
            "restaurants": restaurants,
            "delivery_locations": delivery_locations,
            "delivery_fees": delivery_fees
        }
        
    except Exception as e:
        print(f"❌ Error analyzing data: {e}")
        import traceback
        traceback.print_exc()
        return None

def suggest_data_fixes(data):
    """Suggest fixes for data inconsistencies"""
    print("\n🔧 Analyzing data inconsistencies and suggesting fixes...")
    
    if not data:
        print("❌ No data to analyze")
        return
    
    areas = data["areas"]
    delivery_locations = data["delivery_locations"]
    delivery_fees = data["delivery_fees"]
    
    # Create ID mappings
    area_ids = {int(area["id"]) for area in areas if "id" in area}
    location_ids = {int(location["id"]) for location in delivery_locations if "id" in location}
    
    print(f"Valid Area IDs: {sorted(area_ids)}")
    print(f"Valid Location IDs: {sorted(location_ids)}")
    
    # Check delivery fees for issues
    orphaned_fees = []
    valid_fees = []
    
    for fee in delivery_fees:
        try:
            fee_area_id = int(fee.get("area_id", 0))
            fee_location_id = int(fee.get("location_id", 0))
            
            area_exists = fee_area_id in area_ids
            location_exists = fee_location_id in location_ids
            
            if not area_exists or not location_exists:
                orphaned_fees.append({
                    "fee": fee,
                    "area_exists": area_exists,
                    "location_exists": location_exists
                })
            else:
                valid_fees.append(fee)
                
        except (ValueError, TypeError):
            orphaned_fees.append({
                "fee": fee,
                "area_exists": False,
                "location_exists": False,
                "error": "Invalid ID format"
            })
    
    print(f"\n📊 Delivery Fee Analysis:")
    print(f"  Valid fees: {len(valid_fees)}")
    print(f"  Orphaned fees: {len(orphaned_fees)}")
    
    if orphaned_fees:
        print(f"\n⚠️ Orphaned Delivery Fees:")
        for orphaned in orphaned_fees:
            fee = orphaned["fee"]
            area_id = fee.get("area_id")
            location_id = fee.get("location_id")
            fee_amount = fee.get("fee")
            
            issues = []
            if not orphaned.get("area_exists", True):
                issues.append(f"area {area_id} doesn't exist")
            if not orphaned.get("location_exists", True):
                issues.append(f"location {location_id} doesn't exist")
            if orphaned.get("error"):
                issues.append(orphaned["error"])
            
            print(f"    - Fee {fee_amount} birr for area {area_id} -> location {location_id}")
            print(f"      Issues: {', '.join(issues)}")
    
    # Suggest fixes
    print(f"\n💡 Suggested Fixes:")
    
    if not areas:
        print("  1. ❗ Add areas via maintenance bot")
        print("     - Use maintenance bot to add: Bole Area, Geda Gate Area, Kereyu Area, College Mecheresha Area")
    
    if not delivery_locations:
        print("  2. ❗ Add delivery locations via maintenance bot")
        print("     - Use maintenance bot to add: Applied Library, Federal Dorm, Central Library, etc.")
    
    if orphaned_fees:
        print("  3. ❗ Clean up orphaned delivery fees")
        print("     - Remove fees that reference non-existent areas/locations")
        print("     - Re-add fees with correct area/location IDs")
    
    if areas and delivery_locations and not valid_fees:
        print("  4. ❗ Add delivery fees via maintenance bot")
        print("     - Use maintenance bot to add fees for each area-location combination")
    
    return {
        "valid_fees": valid_fees,
        "orphaned_fees": orphaned_fees,
        "area_ids": area_ids,
        "location_ids": location_ids
    }

def cleanup_orphaned_fees():
    """Clean up orphaned delivery fees"""
    print("\n🧹 Cleaning up orphaned delivery fees...")
    
    try:
        from src.utils.data_sync import cleanup_orphaned_delivery_fees
        
        success = cleanup_orphaned_delivery_fees()
        
        if success:
            print("✅ Orphaned delivery fees cleanup completed")
            return True
        else:
            print("❌ Failed to clean up orphaned delivery fees")
            return False
            
    except Exception as e:
        print(f"❌ Error during cleanup: {e}")
        import traceback
        traceback.print_exc()
        return False

def validate_fixes():
    """Validate that fixes have been applied correctly"""
    print("\n✅ Validating fixes...")
    
    try:
        from src.utils.data_sync import validate_data_consistency
        
        report = validate_data_consistency()
        
        print(f"📊 Post-Fix Validation Report:")
        print(f"  Total Issues: {report['summary']['total_issues']}")
        print(f"  Critical Issues: {report['summary']['critical_issues']}")
        print(f"  Orphaned Fees: {len(report['orphaned_fees'])}")
        print(f"  Missing Fees: {len(report['missing_fees'])}")
        
        if report['summary']['critical_issues'] == 0:
            print("✅ No critical issues found - data consistency improved!")
            return True
        else:
            print(f"⚠️ {report['summary']['critical_issues']} critical issues remain")
            return False
            
    except Exception as e:
        print(f"❌ Error during validation: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Main function to analyze and fix data consistency issues"""
    print("🚀 Firebase Data Consistency Fix Tool")
    print("=" * 50)
    
    # Step 1: Analyze current data
    data = analyze_current_data()
    if not data:
        print("❌ Failed to analyze data. Exiting.")
        return False
    
    # Step 2: Suggest fixes
    analysis = suggest_data_fixes(data)
    if not analysis:
        print("❌ Failed to analyze data inconsistencies. Exiting.")
        return False
    
    # Step 3: Clean up orphaned fees if any exist
    if analysis["orphaned_fees"]:
        print(f"\n🔧 Found {len(analysis['orphaned_fees'])} orphaned fees. Cleaning up...")
        cleanup_success = cleanup_orphaned_fees()
        if not cleanup_success:
            print("⚠️ Cleanup failed, but continuing...")
    
    # Step 4: Validate fixes
    validation_success = validate_fixes()
    
    # Final recommendations
    print("\n" + "=" * 50)
    print("RECOMMENDATIONS")
    print("=" * 50)
    
    if not analysis["area_ids"]:
        print("🔴 CRITICAL: No areas found in Firebase")
        print("   Action: Use maintenance bot to add areas")
    
    if not analysis["location_ids"]:
        print("🔴 CRITICAL: No delivery locations found in Firebase")
        print("   Action: Use maintenance bot to add delivery locations")
    
    if not analysis["valid_fees"]:
        print("🔴 CRITICAL: No valid delivery fees found in Firebase")
        print("   Action: Use maintenance bot to add delivery fees")
    
    if analysis["area_ids"] and analysis["location_ids"] and analysis["valid_fees"]:
        print("🟢 Data structure looks good!")
        print("   The system should now work correctly with Firebase data")
    
    print("\n📝 Next Steps:")
    print("1. Use the maintenance bot to add missing areas, locations, and fees")
    print("2. Run the test script: python test_data_consistency_fix.py")
    print("3. Test actual order placement to verify fixes")
    
    return validation_success

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
