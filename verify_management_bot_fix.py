#!/usr/bin/env python3
"""
Verify Management Bot Fix
Tests if the management bot fix in main.py works correctly
"""

import sys
import os
import time

# Add the project root to the Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def verify_management_bot_fix():
    """Verify that the management bot fix works"""
    print("🧪 VERIFYING MANAGEMENT BOT FIX")
    print("=" * 50)
    
    try:
        # Test the fixed run_management_bot function
        print("1. Testing management bot import from main.py...")
        
        # Import the fixed function
        from main import run_management_bot
        print("✅ run_management_bot imported successfully")
        
        # Test that the function can import the management bot
        print("2. Testing management bot import within function...")
        from src.bots.management_bot import management_bot, register_management_bot_handlers
        print("✅ Management bot imported successfully")
        
        # Test bot connection
        bot_info = management_bot.get_me()
        print(f"✅ Bot connected: @{bot_info.username}")
        
        # Test handler registration
        print("3. Testing handler registration...")
        register_management_bot_handlers()
        print(f"✅ Registered {len(management_bot.message_handlers)} message handlers")
        print(f"✅ Registered {len(management_bot.callback_query_handlers)} callback handlers")
        
        # Test webhook removal
        print("4. Testing webhook removal...")
        try:
            management_bot.remove_webhook()
            print("✅ Webhook removed successfully")
        except Exception as e:
            print(f"⚠️ Webhook removal: {e}")
        
        print("\n" + "=" * 50)
        print("🎉 MANAGEMENT BOT FIX VERIFICATION COMPLETE!")
        print("=" * 50)
        print("✅ All components working correctly")
        print("✅ Bot instance properly imported")
        print("✅ Handlers registered successfully")
        print("✅ Webhook management working")
        print("")
        print("The Management Bot should now respond to commands!")
        print("Try running: python main.py --bot management")
        print("Then send /start to @Wiz_Aroma_Finance_bot")
        print("=" * 50)
        
        return True
        
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = verify_management_bot_fix()
    if success:
        print("\n🎉 Verification completed successfully!")
    else:
        print("\n💥 Verification failed!")
        sys.exit(1)
