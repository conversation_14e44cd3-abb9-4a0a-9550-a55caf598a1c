#!/usr/bin/env python3
"""
Direct Firebase update to add new delivery personnel
"""

import sys
import os
import uuid
import datetime

# Add the project root to the path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def add_personnel_direct():
    """Add personnel directly to Firebase"""
    try:
        from src.firebase_db import set_data, get_data
        
        print("🚚 Adding delivery personnel 1133538088 directly to Firebase...")
        
        # Generate personnel ID
        personnel_id = f"dp_{uuid.uuid4().hex[:8]}"
        
        # Create personnel data
        personnel_data = {
            "personnel_id": personnel_id,
            "name": "New Delivery Personnel",
            "phone_number": "+************",
            "telegram_id": "1133538088",
            "email": "<EMAIL>",
            "service_areas": ["1", "2", "3", "4"],
            "max_capacity": 5,
            "current_capacity": 0,
            "status": "offline",
            "created_at": datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
            "last_active": datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
            "is_verified": True,
            "emergency_contact": None,
            "vehicle_type": "motorcycle",
            "rating": 5.0,
            "total_deliveries": 0,
            "successful_deliveries": 0
        }
        
        # Get existing personnel data
        existing_personnel = get_data("delivery_personnel") or {}
        
        # Check if already exists
        for pid, pdata in existing_personnel.items():
            if pdata.get('telegram_id') == "1133538088":
                print(f"Personnel with Telegram ID 1133538088 already exists: {pid}")
                return pid
        
        # Add new personnel
        existing_personnel[personnel_id] = personnel_data
        
        # Update Firebase
        if set_data("delivery_personnel", existing_personnel):
            print(f"✅ Added personnel to delivery_personnel: {personnel_id}")
        else:
            print("❌ Failed to add to delivery_personnel")
            return None
        
        # Add to availability
        availability_data = get_data("delivery_personnel_availability") or {}
        availability_data[personnel_id] = "offline"
        if set_data("delivery_personnel_availability", availability_data):
            print(f"✅ Added to availability data")
        
        # Add to capacity
        capacity_data = get_data("delivery_personnel_capacity") or {}
        capacity_data[personnel_id] = 0
        if set_data("delivery_personnel_capacity", capacity_data):
            print(f"✅ Added to capacity data")
        
        # Add to zones
        zones_data = get_data("delivery_personnel_zones") or {}
        zones_data[personnel_id] = ["1", "2", "3", "4"]
        if set_data("delivery_personnel_zones", zones_data):
            print(f"✅ Added to zones data")
        
        # Add to performance
        performance_data = get_data("delivery_personnel_performance") or {}
        performance_data[personnel_id] = {
            "total_deliveries": 0,
            "successful_deliveries": 0,
            "average_rating": 5.0,
            "total_distance": 0.0,
            "average_delivery_time": 0.0,
            "last_updated": datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        }
        if set_data("delivery_personnel_performance", performance_data):
            print(f"✅ Added to performance data")
        
        print(f"🎉 Successfully added delivery personnel: {personnel_id}")
        return personnel_id
        
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()
        return None

def verify_addition():
    """Verify the personnel was added correctly"""
    try:
        from src.firebase_db import get_data
        
        print("\n🔍 Verifying addition...")
        
        personnel_data = get_data("delivery_personnel") or {}
        
        for pid, pdata in personnel_data.items():
            if pdata.get('telegram_id') == "1133538088":
                print(f"✅ Found personnel: {pid}")
                print(f"   Name: {pdata.get('name')}")
                print(f"   Telegram ID: {pdata.get('telegram_id')}")
                print(f"   Service Areas: {pdata.get('service_areas')}")
                print(f"   Verified: {pdata.get('is_verified')}")
                return True
        
        print("❌ Personnel not found")
        return False
        
    except Exception as e:
        print(f"❌ Verification error: {e}")
        return False

if __name__ == "__main__":
    print("🚀 DIRECT FIREBASE PERSONNEL ADDITION")
    print("=" * 50)
    
    personnel_id = add_personnel_direct()
    
    if personnel_id:
        verify_addition()
        print(f"\n✅ SUCCESS: Personnel {personnel_id} added with Telegram ID 1133538088")
    else:
        print("\n❌ FAILED to add personnel")
