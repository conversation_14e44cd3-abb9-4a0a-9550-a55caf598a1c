#!/usr/bin/env python3
"""
Test Complete Button Flow for Management Bot
Tests navigation through menus and sub-menus
"""

import sys
import os
import time

# Add the project root to the Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_complete_button_flow():
    """Test complete button flow including sub-menu navigation"""
    print("🔄 TESTING COMPLETE BUTTON FLOW")
    print("=" * 60)
    
    try:
        # Import required modules
        from src.bots.management_bot import management_bot, handle_callback_query
        from telebot.types import CallbackQuery, User, Chat, Message
        
        print("✅ Management bot modules imported successfully")
        
        # Test bot connection
        bot_info = management_bot.get_me()
        print(f"✅ Bot connected: @{bot_info.username}")
        
        # Create mock authorized user
        mock_user = User(
            id=7729984017,
            is_bot=False,
            first_name="Test",
            username="testuser",
            last_name="User",
            language_code="en"
        )
        
        # Create mock chat and message
        mock_chat = Chat(id=7729984017, type="private")
        mock_message = Message(
            message_id=1,
            from_user=mock_user,
            date=int(time.time()),
            chat=mock_chat,
            content_type="text",
            options={},
            json_string=""
        )
        
        # Track all interactions
        interactions = []
        
        # Mock bot methods
        original_answer_callback_query = management_bot.answer_callback_query
        original_edit_message_text = management_bot.edit_message_text
        
        def mock_answer_callback_query(callback_query_id, text=None, show_alert=False, **kwargs):
            interactions.append({
                'type': 'callback_answer',
                'id': callback_query_id,
                'text': text,
                'show_alert': show_alert
            })
            print(f"📞 Callback answered: {text}")
            return True
        
        def mock_edit_message_text(text, chat_id, message_id, **kwargs):
            keyboard_info = ""
            if 'reply_markup' in kwargs:
                markup = kwargs['reply_markup']
                if hasattr(markup, 'keyboard'):
                    keyboard_info = f" (Keyboard: {len(markup.keyboard)} rows)"
            
            interactions.append({
                'type': 'message_edit',
                'text': text,
                'chat_id': chat_id,
                'message_id': message_id,
                'has_keyboard': 'reply_markup' in kwargs
            })
            print(f"✏️ Message edited: {text[:50]}...{keyboard_info}")
            return type('MockMessage', (), {'message_id': message_id})()
        
        # Replace bot methods
        management_bot.answer_callback_query = mock_answer_callback_query
        management_bot.edit_message_text = mock_edit_message_text
        
        # Test complete navigation flow
        navigation_flow = [
            {
                'step': 1,
                'action': 'Open Personnel Management',
                'callback_data': 'mgmt_personnel',
                'expected_callback': '👥 Loading personnel management...',
                'expected_content': 'Personnel Management'
            },
            {
                'step': 2,
                'action': 'Go back to Main Menu',
                'callback_data': 'mgmt_main',
                'expected_callback': '🏢 Loading main menu...',
                'expected_content': 'Wiz Aroma Management Bot'
            },
            {
                'step': 3,
                'action': 'Open Analytics Dashboard',
                'callback_data': 'mgmt_analytics',
                'expected_callback': '📊 Loading analytics dashboard...',
                'expected_content': 'Analytics Dashboard'
            },
            {
                'step': 4,
                'action': 'Go back to Main Menu',
                'callback_data': 'mgmt_main',
                'expected_callback': '🏢 Loading main menu...',
                'expected_content': 'Wiz Aroma Management Bot'
            },
            {
                'step': 5,
                'action': 'Test Reports (Placeholder)',
                'callback_data': 'mgmt_reports',
                'expected_callback': '📈 Reports feature coming soon!',
                'expected_content': None
            },
            {
                'step': 6,
                'action': 'Test Earnings (Placeholder)',
                'callback_data': 'mgmt_earnings',
                'expected_callback': '💰 Earnings feature coming soon!',
                'expected_content': None
            }
        ]
        
        print(f"\n🔄 Testing {len(navigation_flow)} navigation steps...")
        
        all_steps_passed = True
        
        for step_info in navigation_flow:
            print(f"\n--- Step {step_info['step']}: {step_info['action']} ---")
            
            # Clear previous interactions
            interactions.clear()
            
            # Create mock callback query
            mock_callback = CallbackQuery(
                id=f"test_step_{step_info['step']}",
                from_user=mock_user,
                message=mock_message,
                data=step_info['callback_data'],
                chat_instance="test_chat",
                json_string=""
            )
            
            try:
                # Execute the callback
                handle_callback_query(mock_callback)
                
                # Verify callback answer
                callback_answers = [i for i in interactions if i['type'] == 'callback_answer']
                if callback_answers:
                    answer = callback_answers[0]
                    if answer['text'] == step_info['expected_callback']:
                        print(f"✅ Callback answered correctly")
                    else:
                        print(f"❌ Wrong callback text. Expected: {step_info['expected_callback']}, Got: {answer['text']}")
                        all_steps_passed = False
                else:
                    print(f"❌ No callback answer received")
                    all_steps_passed = False
                
                # Verify message edit (if expected)
                message_edits = [i for i in interactions if i['type'] == 'message_edit']
                if step_info['expected_content']:
                    if message_edits:
                        edit = message_edits[0]
                        if step_info['expected_content'] in edit['text']:
                            print(f"✅ Message content correct")
                            if edit['has_keyboard']:
                                print(f"✅ Keyboard included")
                            else:
                                print(f"⚠️ No keyboard found")
                        else:
                            print(f"❌ Wrong message content")
                            all_steps_passed = False
                    else:
                        print(f"❌ Expected message edit but none received")
                        all_steps_passed = False
                else:
                    # Placeholder functions shouldn't edit messages
                    if message_edits:
                        print(f"⚠️ Unexpected message edit for placeholder")
                    else:
                        print(f"✅ No message edit (correct for placeholder)")
                
                # Small delay between steps
                time.sleep(0.1)
                
            except Exception as e:
                print(f"❌ Error in step {step_info['step']}: {e}")
                all_steps_passed = False
        
        # Test sub-menu navigation (Personnel Management)
        print(f"\n🔍 Testing Personnel Management sub-menu...")
        
        personnel_submenu_tests = [
            ('pers_add', 'Add Personnel'),
            ('pers_remove', 'Remove Personnel'),
            ('pers_list', 'List Personnel'),
            ('pers_search', 'Search Personnel')
        ]
        
        for callback_data, action_name in personnel_submenu_tests:
            print(f"\n--- Testing: {action_name} ---")
            interactions.clear()
            
            mock_callback = CallbackQuery(
                id=f"test_{callback_data}",
                from_user=mock_user,
                message=mock_message,
                data=callback_data,
                chat_instance="test_chat",
                json_string=""
            )
            
            try:
                handle_callback_query(mock_callback)
                
                # Check if callback was handled (should have some response)
                if interactions:
                    print(f"✅ {action_name} handled successfully")
                else:
                    print(f"⚠️ {action_name} - no response (may need implementation)")
                    
            except Exception as e:
                print(f"❌ Error testing {action_name}: {e}")
        
        # Restore original methods
        management_bot.answer_callback_query = original_answer_callback_query
        management_bot.edit_message_text = original_edit_message_text
        
        print("\n" + "=" * 60)
        if all_steps_passed:
            print("🎉 COMPLETE BUTTON FLOW WORKING PERFECTLY!")
            print("✅ All navigation steps work correctly")
            print("✅ Loading indicators are dismissed properly")
            print("✅ Menu transitions are smooth")
            print("✅ Placeholder functions provide feedback")
        else:
            print("❌ SOME NAVIGATION ISSUES DETECTED")
        print("=" * 60)
        
        return all_steps_passed
        
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = test_complete_button_flow()
    if success:
        print("\n🎉 Complete button flow verified!")
    else:
        print("\n💥 Button flow test failed!")
        sys.exit(1)
