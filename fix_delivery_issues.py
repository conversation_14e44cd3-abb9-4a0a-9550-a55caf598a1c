#!/usr/bin/env python3
"""
Fix both delivery system issues:
1. Add new delivery personnel to Firebase
2. Test customer confirmation workflow
"""

import sys
import os
import datetime
import uuid

sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def add_delivery_personnel():
    """Add new delivery personnel with Telegram ID 1133538088"""
    print("🚚 ADDING NEW DELIVERY PERSONNEL")
    print("=" * 50)
    
    try:
        from src.firebase_db import get_data, update_data
        
        # Check if personnel already exists
        personnel_data = get_data("delivery_personnel") or {}
        telegram_id = "1133538088"
        
        # Check if already exists
        for pid, pdata in personnel_data.items():
            if pdata.get('telegram_id') == telegram_id:
                print(f"✅ Personnel with Telegram ID {telegram_id} already exists: {pid}")
                return pid
        
        # Generate new personnel ID
        personnel_id = f"dp_{uuid.uuid4().hex[:8]}"
        
        # Create personnel record
        new_personnel = {
            "personnel_id": personnel_id,
            "name": "New Delivery Personnel",
            "phone_number": "+************",
            "telegram_id": telegram_id,
            "email": "<EMAIL>",
            "service_areas": ["1", "2", "3", "4"],
            "max_capacity": 5,
            "current_capacity": 0,
            "status": "offline",
            "created_at": datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
            "last_active": datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S"),
            "is_verified": True,
            "emergency_contact": None,
            "vehicle_type": "motorcycle",
            "rating": 5.0,
            "total_deliveries": 0,
            "successful_deliveries": 0
        }
        
        # Update Firebase with new personnel
        if update_data(f"delivery_personnel/{personnel_id}", new_personnel):
            print(f"✅ Added personnel to Firebase: {personnel_id}")
        else:
            print("❌ Failed to add personnel to Firebase")
            return None
        
        # Add to availability
        if update_data(f"delivery_personnel_availability/{personnel_id}", "offline"):
            print(f"✅ Added availability status")
        
        # Add to capacity
        if update_data(f"delivery_personnel_capacity/{personnel_id}", 0):
            print(f"✅ Added capacity data")
        
        # Add to zones
        if update_data(f"delivery_personnel_zones/{personnel_id}", ["1", "2", "3", "4"]):
            print(f"✅ Added zones data")
        
        # Add to performance
        performance_data = {
            "total_deliveries": 0,
            "successful_deliveries": 0,
            "average_rating": 5.0,
            "total_distance": 0.0,
            "average_delivery_time": 0.0,
            "last_updated": datetime.datetime.now().strftime("%Y-%m-%d %H:%M:%S")
        }
        if update_data(f"delivery_personnel_performance/{personnel_id}", performance_data):
            print(f"✅ Added performance data")
        
        print(f"🎉 Successfully added delivery personnel: {personnel_id}")
        return personnel_id
        
    except Exception as e:
        print(f"❌ Error adding personnel: {e}")
        import traceback
        traceback.print_exc()
        return None

def test_authorization_fix():
    """Test that the authorization fix works"""
    print("\n🔐 TESTING AUTHORIZATION FIX")
    print("=" * 50)
    
    try:
        from src.config import DELIVERY_BOT_AUTHORIZED_IDS
        
        if 1133538088 in DELIVERY_BOT_AUTHORIZED_IDS:
            print("✅ Telegram ID 1133538088 is now authorized for delivery bot")
            return True
        else:
            print(f"❌ Telegram ID 1133538088 NOT in authorized list: {DELIVERY_BOT_AUTHORIZED_IDS}")
            return False
            
    except Exception as e:
        print(f"❌ Error testing authorization: {e}")
        return False

def test_customer_confirmation_imports():
    """Test customer confirmation workflow imports"""
    print("\n👤 TESTING CUSTOMER CONFIRMATION IMPORTS")
    print("=" * 50)
    
    try:
        # Test order tracking bot imports
        from src.bots.order_track_bot import send_customer_confirmation_request
        print("✅ Can import send_customer_confirmation_request")
        
        # Test user bot import
        from src.bot_instance import bot
        print("✅ Can import user bot instance")
        
        # Test order handler import
        from src.handlers.order_handlers import handle_delivery_confirmation
        print("✅ Can import delivery confirmation handler")
        
        return True
        
    except Exception as e:
        print(f"❌ Error testing customer confirmation imports: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_delivery_bot_recognition():
    """Test if delivery bot recognizes new personnel"""
    print("\n🤖 TESTING DELIVERY BOT RECOGNITION")
    print("=" * 50)
    
    try:
        from src.bots.delivery_bot import get_personnel_by_telegram_id
        
        telegram_id = 1133538088  # As integer
        personnel = get_personnel_by_telegram_id(telegram_id)
        
        if personnel:
            print(f"✅ Delivery bot recognizes personnel:")
            print(f"   Personnel ID: {personnel.personnel_id}")
            print(f"   Name: {personnel.name}")
            print(f"   Telegram ID: {personnel.telegram_id}")
            return True
        else:
            print(f"❌ Delivery bot does NOT recognize Telegram ID {telegram_id}")
            return False
            
    except Exception as e:
        print(f"❌ Error testing delivery bot recognition: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Main function to fix both issues"""
    print("🔧 FIXING DELIVERY SYSTEM ISSUES")
    print("=" * 80)
    
    # Fix 1: Add delivery personnel
    personnel_id = add_delivery_personnel()
    
    # Fix 2: Test authorization fix
    auth_ok = test_authorization_fix()
    
    # Fix 3: Test customer confirmation
    confirmation_ok = test_customer_confirmation_imports()
    
    # Fix 4: Test delivery bot recognition
    recognition_ok = test_delivery_bot_recognition()
    
    # Summary
    print("\n" + "=" * 80)
    print("📊 FIX RESULTS SUMMARY:")
    print(f"   Personnel Addition: {'✅ Success' if personnel_id else '❌ Failed'}")
    print(f"   Authorization Fix: {'✅ Success' if auth_ok else '❌ Failed'}")
    print(f"   Confirmation Imports: {'✅ Success' if confirmation_ok else '❌ Failed'}")
    print(f"   Bot Recognition: {'✅ Success' if recognition_ok else '❌ Failed'}")
    
    if personnel_id and auth_ok and confirmation_ok and recognition_ok:
        print("\n🎉 ALL FIXES SUCCESSFUL!")
        print("\n📋 READY FOR TESTING:")
        print("1. Telegram ID 1133538088 can now access delivery bot")
        print("2. Customer confirmation workflow is properly configured")
        print("3. All three delivery personnel can complete full workflow")
        
        return True
    else:
        print("\n⚠️  Some fixes failed. Please review the errors above.")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
