#!/usr/bin/env python3
"""
Complete Delivery Personnel System Reset Script
Clears all delivery personnel data and starts fresh
"""

import sys
import os

# Add the project root to the Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def clear_delivery_personnel_collections():
    """Clear all delivery personnel related Firebase collections"""
    print("🧹 CLEARING DELIVERY PERSONNEL COLLECTIONS")
    print("=" * 50)
    
    try:
        from src.firebase_db import set_data, get_data
        
        # List of all delivery personnel related collections
        collections_to_clear = [
            "delivery_personnel",
            "delivery_personnel_earnings", 
            "delivery_personnel_availability",
            "delivery_personnel_capacity",
            "delivery_personnel_zones",
            "delivery_personnel_performance",
            "authorized_delivery_personnel"
        ]
        
        cleared_collections = []
        failed_collections = []
        
        for collection in collections_to_clear:
            print(f"🔄 Clearing collection: {collection}")
            
            # Get current data to see what we're clearing
            current_data = get_data(collection) or {}
            record_count = len(current_data)
            
            if record_count > 0:
                print(f"   📊 Found {record_count} records to clear")
                
                # Clear the collection by setting it to empty dict
                success = set_data(collection, {})
                
                if success:
                    print(f"   ✅ Successfully cleared {collection}")
                    cleared_collections.append((collection, record_count))
                else:
                    print(f"   ❌ Failed to clear {collection}")
                    failed_collections.append(collection)
            else:
                print(f"   ✅ Collection {collection} already empty")
                cleared_collections.append((collection, 0))
        
        print(f"\n📊 CLEARING SUMMARY:")
        print(f"✅ Successfully cleared: {len(cleared_collections)} collections")
        for collection, count in cleared_collections:
            print(f"   - {collection}: {count} records removed")
        
        if failed_collections:
            print(f"❌ Failed to clear: {len(failed_collections)} collections")
            for collection in failed_collections:
                print(f"   - {collection}")
            return False
        
        return True
        
    except Exception as e:
        print(f"❌ Error clearing collections: {e}")
        import traceback
        traceback.print_exc()
        return False

def clear_authorization_cache():
    """Clear delivery bot authorization cache"""
    print("\n🔄 CLEARING AUTHORIZATION CACHE")
    print("=" * 50)
    
    try:
        from src.bots.delivery_bot import clear_authorization_cache
        clear_authorization_cache()
        print("✅ Authorization cache cleared successfully")
        return True
    except Exception as e:
        print(f"❌ Error clearing authorization cache: {e}")
        return False

def verify_system_is_clean():
    """Verify that all delivery personnel data has been cleared"""
    print("\n🔍 VERIFYING SYSTEM IS CLEAN")
    print("=" * 50)
    
    try:
        from src.firebase_db import get_data
        from src.bots.delivery_bot import get_authorized_delivery_ids_from_firebase
        
        # Check all collections are empty
        collections_to_check = [
            "delivery_personnel",
            "delivery_personnel_earnings", 
            "delivery_personnel_availability",
            "delivery_personnel_capacity",
            "delivery_personnel_zones",
            "delivery_personnel_performance",
            "authorized_delivery_personnel"
        ]
        
        all_clean = True
        
        for collection in collections_to_check:
            data = get_data(collection) or {}
            record_count = len(data)
            
            if record_count == 0:
                print(f"✅ {collection}: Empty ({record_count} records)")
            else:
                print(f"❌ {collection}: NOT empty ({record_count} records)")
                all_clean = False
        
        # Check authorization system
        print(f"\n🔐 Checking authorization system...")
        authorized_ids = get_authorized_delivery_ids_from_firebase()
        
        # Should only contain admin IDs (no delivery personnel)
        admin_only_ids = [7729984017]  # Admin fallback IDs
        
        if set(authorized_ids) == set(admin_only_ids):
            print(f"✅ Authorization system clean: {authorized_ids}")
        else:
            print(f"❌ Authorization system contains delivery personnel: {authorized_ids}")
            all_clean = False
        
        if all_clean:
            print(f"\n🎉 SYSTEM IS COMPLETELY CLEAN!")
            print("✅ All delivery personnel data removed")
            print("✅ Authorization cache cleared")
            print("✅ Ready for fresh personnel addition")
        else:
            print(f"\n⚠️ SYSTEM NOT COMPLETELY CLEAN")
            print("Some data may still exist")
        
        return all_clean
        
    except Exception as e:
        print(f"❌ Error verifying system: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_management_bot_import():
    """Test that management bot can be imported and is ready for use"""
    print("\n🧪 TESTING MANAGEMENT BOT READINESS")
    print("=" * 50)
    
    try:
        from src.bots.management_bot import validate_telegram_id, add_authorized_delivery_personnel
        print("✅ Management bot imports successfully")
        
        # Test validation function
        test_result = validate_telegram_id("1234567890")
        print(f"✅ Validation function works: {test_result}")
        
        return True
        
    except Exception as e:
        print(f"❌ Management bot import failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Execute complete delivery personnel system reset"""
    print("🚀 DELIVERY PERSONNEL SYSTEM RESET")
    print("=" * 60)
    print("This will completely clear all delivery personnel data!")
    print("=" * 60)
    
    # Step 1: Clear all collections
    step1_success = clear_delivery_personnel_collections()
    
    # Step 2: Clear authorization cache
    step2_success = clear_authorization_cache()
    
    # Step 3: Verify system is clean
    step3_success = verify_system_is_clean()
    
    # Step 4: Test management bot readiness
    step4_success = test_management_bot_import()
    
    # Final summary
    print("\n" + "=" * 60)
    print("📊 RESET SUMMARY")
    print("=" * 60)
    
    steps = [
        ("Clear Collections", step1_success),
        ("Clear Cache", step2_success), 
        ("Verify Clean", step3_success),
        ("Test Management Bot", step4_success)
    ]
    
    passed = sum(success for _, success in steps)
    total = len(steps)
    
    for step_name, success in steps:
        status = "✅ PASSED" if success else "❌ FAILED"
        print(f"{status}: {step_name}")
    
    print(f"\nOverall: {passed}/{total} steps completed successfully")
    
    if all(success for _, success in steps):
        print("\n🎉 SYSTEM RESET COMPLETE!")
        print("✅ All delivery personnel data cleared")
        print("✅ System ready for fresh personnel addition via management bot")
        print("\n📋 NEXT STEPS:")
        print("1. Use management bot to add new delivery personnel")
        print("2. Verify authorization works correctly")
        print("3. Test order broadcast notifications")
        return True
    else:
        print("\n⚠️ RESET INCOMPLETE")
        print("Some steps failed. Please review errors above.")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
