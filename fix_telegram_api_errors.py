#!/usr/bin/env python3
"""
Fix all remaining Telegram API errors in the management bot by replacing
direct edit_message_text calls with safe_edit_message calls
"""

import sys
import os
import re

# Add the project root to the Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def fix_management_bot_telegram_errors():
    """Fix all Telegram API errors in management bot"""
    print("🔧 FIXING TELEGRAM API ERRORS IN MANAGEMENT BOT")
    print("=" * 60)
    
    file_path = "src/bots/management_bot.py"
    
    try:
        # Read the file
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        original_content = content
        
        # Pattern to match direct edit_message_text calls
        pattern = r'management_bot\.edit_message_text\(\s*([^,]+),\s*call\.message\.chat\.id,\s*call\.message\.message_id,\s*(?:reply_markup=([^,\)]+),?\s*)?(?:parse_mode=([^,\)]+))?\s*\)'
        
        # Find all matches
        matches = list(re.finditer(pattern, content, re.MULTILINE | re.DOTALL))
        
        print(f"📊 Found {len(matches)} direct edit_message_text calls to fix")
        
        # Replace from end to beginning to maintain positions
        for i, match in enumerate(reversed(matches)):
            start, end = match.span()
            text_param = match.group(1).strip()
            keyboard_param = match.group(2).strip() if match.group(2) else "None"
            parse_mode_param = match.group(3).strip() if match.group(3) else "'Markdown'"
            
            # Create the replacement
            if keyboard_param == "None":
                replacement = f"""# Use safe message editing with fallback mechanisms
        if not safe_edit_message(call, {text_param}, None, parse_mode={parse_mode_param}):
            management_bot.answer_callback_query(call.id, "❌ Display error. Please try again.", show_alert=True)"""
            else:
                replacement = f"""# Use safe message editing with fallback mechanisms
        if not safe_edit_message(call, {text_param}, {keyboard_param}, parse_mode={parse_mode_param}):
            management_bot.answer_callback_query(call.id, "❌ Display error. Please try again.", show_alert=True)"""
            
            # Replace the content
            content = content[:start] + replacement + content[end:]
            
            print(f"   ✅ Fixed call #{len(matches) - i}")
        
        # Additional specific fixes for error handling patterns
        
        # Fix error handling in handle_analytics_error
        error_pattern = r'management_bot\.edit_message_text\(\s*error_text,\s*call\.message\.chat\.id,\s*call\.message\.message_id,\s*reply_markup=keyboard,\s*parse_mode=\'Markdown\'\s*\)'
        if re.search(error_pattern, content):
            content = re.sub(
                error_pattern,
                """# Use safe message editing for error display
        if not safe_edit_message(call, error_text, keyboard):
            management_bot.answer_callback_query(call.id, "❌ Error displaying message. Please try again.", show_alert=True)""",
                content
            )
            print("   ✅ Fixed error handling pattern")
        
        # Fix fallback message patterns
        fallback_pattern = r'management_bot\.edit_message_text\(\s*f"⚠️ \*\*Display Issue\*\*\\n\\n{message}\\n\\nPlease use the buttons below to continue\.",\s*call\.message\.chat\.id,\s*call\.message\.message_id,\s*reply_markup=keyboard\s*\)'
        if re.search(fallback_pattern, content):
            content = re.sub(
                fallback_pattern,
                """# Use safe message editing for display issues
        if not safe_edit_message(call, f"⚠️ **Display Issue**\\n\\n{message}\\n\\nPlease use the buttons below to continue.", keyboard, parse_mode=None):
            management_bot.answer_callback_query(call.id, "❌ Critical display error.", show_alert=True)""",
                content
            )
            print("   ✅ Fixed fallback message pattern")
        
        # Write the fixed content back
        if content != original_content:
            with open(file_path, 'w', encoding='utf-8') as f:
                f.write(content)
            
            print(f"✅ Successfully fixed {len(matches)} Telegram API calls")
            print(f"📝 Updated file: {file_path}")
            return True
        else:
            print("ℹ️ No changes needed")
            return True
            
    except Exception as e:
        print(f"❌ Error fixing Telegram API errors: {e}")
        import traceback
        traceback.print_exc()
        return False

def add_missing_helper_functions():
    """Add any missing helper functions for safe message editing"""
    print("\n🔧 ADDING MISSING HELPER FUNCTIONS")
    print("=" * 60)
    
    file_path = "src/bots/management_bot.py"
    
    try:
        with open(file_path, 'r', encoding='utf-8') as f:
            content = f.read()
        
        # Check if safe_edit_message_with_fallback exists
        if 'def safe_edit_message_with_fallback' not in content:
            # Add the function after safe_edit_message
            safe_edit_pos = content.find('def safe_edit_message(')
            if safe_edit_pos != -1:
                # Find the end of the safe_edit_message function
                func_end = content.find('\ndef ', safe_edit_pos + 1)
                if func_end == -1:
                    func_end = len(content)
                
                new_function = '''
def safe_edit_message_with_fallback(bot, chat_id, message_id, text, reply_markup=None, parse_mode='Markdown'):
    """Safe message editing with comprehensive fallback for any bot instance"""
    try:
        # Validate message length first
        if not validate_message_length(text):
            text, was_truncated = truncate_message_content(text)
            if was_truncated and reply_markup:
                # Add "Show More" button if content was truncated
                reply_markup.add(types.InlineKeyboardButton("📄 Show More", callback_data="show_more_details"))
        
        # Try to edit the message
        bot.edit_message_text(
            text,
            chat_id,
            message_id,
            reply_markup=reply_markup,
            parse_mode=parse_mode
        )
        return True
        
    except Exception as e:
        error_msg = str(e).lower()
        
        if "message is not modified" in error_msg:
            # Add timestamp to force change
            import random
            text_with_update = f"{text}\\n🔄 Updated {random.randint(1, 999)}"
            try:
                bot.edit_message_text(
                    text_with_update,
                    chat_id,
                    message_id,
                    reply_markup=reply_markup,
                    parse_mode=parse_mode
                )
                return True
            except:
                pass
        
        elif "message too long" in error_msg:
            # Truncate and try again
            short_text, _ = truncate_message_content(text, max_length=3500)
            try:
                bot.edit_message_text(
                    short_text,
                    chat_id,
                    message_id,
                    reply_markup=reply_markup,
                    parse_mode=None  # Remove formatting to save space
                )
                return True
            except:
                pass
        
        # Final fallback - send new message
        try:
            emergency_text = create_emergency_fallback_message(text)
            bot.send_message(
                chat_id,
                f"📊 **Update**\\n\\n{emergency_text}",
                reply_markup=reply_markup,
                parse_mode=None
            )
            return True
        except:
            return False

'''
                content = content[:func_end] + new_function + content[func_end:]
                
                with open(file_path, 'w', encoding='utf-8') as f:
                    f.write(content)
                
                print("✅ Added safe_edit_message_with_fallback function")
                return True
        
        print("ℹ️ Helper functions already exist")
        return True
        
    except Exception as e:
        print(f"❌ Error adding helper functions: {e}")
        return False

def test_telegram_api_fixes():
    """Test that the Telegram API fixes work correctly"""
    print("\n🧪 TESTING TELEGRAM API FIXES")
    print("=" * 60)
    
    try:
        from src.bots.management_bot import (
            validate_message_length,
            truncate_message_content,
            content_has_changed,
            safe_edit_message
        )
        
        # Test message length validation
        short_msg = "Short message"
        long_msg = "A" * 5000
        
        assert validate_message_length(short_msg) == True
        assert validate_message_length(long_msg) == False
        print("✅ Message length validation working")
        
        # Test message truncation
        truncated, was_truncated = truncate_message_content(long_msg, max_length=100)
        assert len(truncated) <= 100
        assert was_truncated == True
        print("✅ Message truncation working")
        
        # Test content change detection
        current = "Current content"
        new_same = "Current content"
        new_different = "Different content"
        
        assert content_has_changed(current, new_same, None, None) == False
        assert content_has_changed(current, new_different, None, None) == True
        print("✅ Content change detection working")
        
        print("🎉 All Telegram API fixes are working correctly!")
        return True
        
    except Exception as e:
        print(f"❌ Error testing fixes: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Run all Telegram API error fixes"""
    print("🚀 TELEGRAM API ERROR FIXES")
    print("=" * 70)
    
    steps = [
        ("Fix Management Bot Telegram Errors", fix_management_bot_telegram_errors),
        ("Add Missing Helper Functions", add_missing_helper_functions),
        ("Test Telegram API Fixes", test_telegram_api_fixes)
    ]
    
    results = []
    for step_name, step_func in steps:
        try:
            print(f"\\n🔄 STEP: {step_name}")
            result = step_func()
            results.append(result)
            status = "✅ SUCCESS" if result else "❌ FAILED"
            print(f"{status}: {step_name}")
        except Exception as e:
            print(f"❌ ERROR in {step_name}: {e}")
            results.append(False)
    
    print("\\n" + "=" * 70)
    print("📊 FIX SUMMARY")
    print("=" * 70)
    
    passed = sum(results)
    total = len(results)
    
    for i, (step_name, _) in enumerate(steps):
        status = "✅ SUCCESS" if results[i] else "❌ FAILED"
        print(f"{status}: {step_name}")
    
    print(f"\\nOverall: {passed}/{total} steps completed successfully")
    
    if all(results):
        print("\\n🎉 TELEGRAM API FIXES COMPLETE!")
        print("✅ All direct edit_message_text calls replaced with safe alternatives")
        print("✅ Content change detection implemented")
        print("✅ Message length validation added")
        print("✅ Comprehensive error handling in place")
        print("\\n📋 BENEFITS:")
        print("• No more 'message is not modified' errors")
        print("• No more 'MESSAGE_TOO_LONG' errors")
        print("• Graceful fallback mechanisms")
        print("• Improved user experience with refresh functionality")
        return True
    else:
        print("\\n⚠️ FIXES INCOMPLETE")
        print("Some steps failed. Please review errors above.")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
