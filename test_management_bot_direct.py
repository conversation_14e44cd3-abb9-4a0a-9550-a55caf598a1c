#!/usr/bin/env python3
"""
Direct Management Bot Test
Tests management bot by simulating a message
"""

import sys
import os

# Add the project root to the Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_management_bot_direct():
    """Test management bot by simulating a message"""
    print("🧪 DIRECT MANAGEMENT BOT TEST")
    print("=" * 40)
    
    try:
        # Import required modules
        from src.bots.management_bot import management_bot, handle_start, is_authorized_user
        
        print("✅ Management bot imported")
        
        # Test bot connection
        bot_info = management_bot.get_me()
        print(f"✅ Bot: @{bot_info.username}")
        
        # Test authorization
        test_user_id = 7729984017
        is_auth = is_authorized_user(test_user_id)
        print(f"✅ Authorization for {test_user_id}: {is_auth}")
        
        # Create a mock message object
        class MockMessage:
            def __init__(self, user_id, text):
                self.from_user = MockUser(user_id)
                self.text = text
                self.chat = MockChat(user_id)
        
        class MockUser:
            def __init__(self, user_id):
                self.id = user_id
        
        class MockChat:
            def __init__(self, chat_id):
                self.id = chat_id
        
        # Test the handle_start function directly
        print("\n🧪 Testing handle_start function...")
        mock_message = MockMessage(7729984017, "/start")
        
        # Override the send_message function to capture output
        original_send_message = management_bot.send_message
        captured_messages = []
        
        def mock_send_message(chat_id, text, **kwargs):
            captured_messages.append({
                'chat_id': chat_id,
                'text': text,
                'kwargs': kwargs
            })
            print(f"📤 Bot would send: {text[:100]}...")
            if 'reply_markup' in kwargs:
                print(f"📋 With inline keyboard: {bool(kwargs['reply_markup'])}")
                if hasattr(kwargs['reply_markup'], 'keyboard'):
                    print(f"   Keyboard buttons: {len(kwargs['reply_markup'].keyboard)} rows")
            if 'parse_mode' in kwargs:
                print(f"📝 Parse mode: {kwargs['parse_mode']}")
        
        management_bot.send_message = mock_send_message
        
        # Call the handler
        try:
            handle_start(mock_message)
            print("✅ handle_start executed successfully")
            
            if captured_messages:
                print(f"✅ Bot generated {len(captured_messages)} response(s)")
                for i, msg in enumerate(captured_messages):
                    print(f"   Response {i+1}: {msg['text'][:50]}...")
            else:
                print("❌ No responses generated")
                
        except Exception as e:
            print(f"❌ Error in handle_start: {e}")
            import traceback
            traceback.print_exc()
        
        # Restore original function
        management_bot.send_message = original_send_message
        
        return True
        
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = test_management_bot_direct()
    if success:
        print("\n🎉 Direct test completed!")
    else:
        print("\n💥 Direct test failed!")
        sys.exit(1)
