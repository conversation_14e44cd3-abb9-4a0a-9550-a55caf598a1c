#!/usr/bin/env python3
"""
Final Management Bot Test
Direct test of the management bot with proper initialization
"""

import sys
import os
import time
import signal

# Add the project root to the Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_management_bot_final():
    """Final test of management bot functionality"""
    print("🧪 FINAL MANAGEMENT BOT TEST")
    print("=" * 50)
    
    try:
        # Load data first
        print("📥 Loading data...")
        from src.data_storage import load_user_data
        load_user_data()
        print("✅ Data loaded successfully")
        
        # Import management bot
        print("🤖 Importing management bot...")
        from src.bots.management_bot import management_bot, register_management_bot_handlers
        print("✅ Management bot imported")
        
        # Test bot connection
        bot_info = management_bot.get_me()
        print(f"✅ Bot connected: @{bot_info.username}")
        
        # Clear existing handlers
        management_bot.message_handlers.clear()
        management_bot.callback_query_handlers.clear()
        print("✅ Cleared existing handlers")
        
        # Register management bot handlers
        register_management_bot_handlers()
        print(f"✅ Registered {len(management_bot.message_handlers)} message handlers")
        print(f"✅ Registered {len(management_bot.callback_query_handlers)} callback handlers")
        
        # Add test handler for verification
        @management_bot.message_handler(commands=['ping'])
        def ping_test(message):
            user_id = message.from_user.id
            if user_id == 7729984017:
                management_bot.reply_to(message, "🏓 Pong! Management bot is working perfectly!")
            else:
                management_bot.reply_to(message, "❌ Access denied.")
            print(f"📨 Ping received from user {user_id}")
        
        print("✅ Added ping test handler")
        
        # Remove webhook to ensure polling works
        try:
            management_bot.remove_webhook()
            print("✅ Webhook removed")
            time.sleep(2)  # Wait for webhook removal
        except Exception as e:
            print(f"⚠️ Webhook removal: {e}")
        
        print("\n" + "=" * 50)
        print("🚀 MANAGEMENT BOT IS NOW RUNNING!")
        print("=" * 50)
        print("Bot: @Wiz_Aroma_Finance_bot")
        print("Authorized User ID: 7729984017")
        print("")
        print("Available Commands:")
        print("  /start - Management interface")
        print("  /help  - Show help")
        print("  /ping  - Test response")
        print("")
        print("Press Ctrl+C to stop the bot...")
        print("=" * 50)
        
        # Set up graceful shutdown
        def signal_handler(sig, frame):
            print("\n⏹️ Shutting down management bot...")
            management_bot.stop_polling()
            print("✅ Management bot stopped")
            sys.exit(0)
        
        signal.signal(signal.SIGINT, signal_handler)
        
        # Start polling with robust settings
        try:
            management_bot.infinity_polling(
                timeout=30,
                long_polling_timeout=30,
                none_stop=True,
                interval=1,
                allowed_updates=None
            )
        except KeyboardInterrupt:
            print("\n⏹️ Bot stopped by user")
        except Exception as e:
            print(f"\n❌ Polling error: {e}")
            import traceback
            traceback.print_exc()
        
        return True
        
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    test_management_bot_final()
