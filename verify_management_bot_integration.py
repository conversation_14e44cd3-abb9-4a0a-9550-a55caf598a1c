#!/usr/bin/env python3
"""
Final verification script to test management bot integration
Simulates the actual management bot workflow for adding delivery personnel
"""

import sys
import os

# Add the project root to the Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def simulate_management_bot_workflow():
    """Simulate the exact workflow that happens in the management bot"""
    print("🤖 SIMULATING MANAGEMENT BOT WORKFLOW")
    print("=" * 50)
    
    try:
        # Import the exact functions used by management bot
        from src.bots.management_bot import (
            validate_telegram_id, 
            validate_name, 
            validate_phone_number,
            add_authorized_delivery_personnel
        )
        from src.firebase_db import get_data, set_data
        from src.data_models import DeliveryPersonnel
        import uuid
        
        # Simulate user input data (like from management bot form)
        personnel_data = {
            'telegram id': '5555666777',  # String input from user
            'name': 'Test Driver',
            'phone': '+************'
        }
        
        print(f"📝 Simulating user input:")
        print(f"   Telegram ID: '{personnel_data['telegram id']}' (type: {type(personnel_data['telegram id'])})")
        print(f"   Name: {personnel_data['name']}")
        print(f"   Phone: {personnel_data['phone']}")
        
        # Step 1: Validate input data (exactly like management bot)
        telegram_id_str = personnel_data['telegram id']
        name = personnel_data['name']
        phone = personnel_data['phone']
        
        print(f"\n🔍 Step 1: Validation")
        
        # Validate Telegram ID
        if not validate_telegram_id(telegram_id_str):
            print(f"❌ Telegram ID validation failed")
            return False
        print(f"✅ Telegram ID validation passed")
        
        # Convert to integer for consistent data type handling (THE FIX)
        telegram_id = int(telegram_id_str)
        print(f"🔄 Converted to integer: {telegram_id} (type: {type(telegram_id)})")
        
        # Validate name
        if not validate_name(name):
            print(f"❌ Name validation failed")
            return False
        print(f"✅ Name validation passed")
        
        # Validate phone
        if not validate_phone_number(phone):
            print(f"❌ Phone validation failed")
            return False
        print(f"✅ Phone validation passed")
        
        # Step 2: Check for duplicates (exactly like management bot)
        print(f"\n🔍 Step 2: Duplicate Check")
        existing_personnel = get_data("delivery_personnel") or {}
        for pid, pdata in existing_personnel.items():
            existing_tid = pdata.get('telegram_id')
            if existing_tid and (str(existing_tid) == str(telegram_id) or int(existing_tid) == telegram_id):
                print(f"❌ Telegram ID {telegram_id} already exists")
                return False
        print(f"✅ No duplicates found")
        
        # Step 3: Create personnel (exactly like management bot)
        print(f"\n🔍 Step 3: Create Personnel")
        personnel_id = f"dp_{uuid.uuid4().hex[:8]}"
        
        personnel = DeliveryPersonnel(personnel_id)
        personnel.name = name
        personnel.phone_number = phone
        personnel.telegram_id = str(telegram_id)  # Store as string in Firebase
        personnel.service_areas = ['area_bole', 'area_4kilo', 'area_6kilo']
        personnel.vehicle_type = 'motorcycle'
        personnel.max_capacity = 5
        personnel.status = 'available'
        personnel.is_verified = True
        
        # Save to Firebase
        personnel_dict = personnel.to_dict()
        existing_personnel[personnel_id] = personnel_dict
        
        success = set_data("delivery_personnel", existing_personnel)
        if not success:
            print(f"❌ Failed to save personnel to Firebase")
            return False
        print(f"✅ Personnel saved to Firebase: {personnel_id}")
        
        # Step 4: Add to authorized delivery personnel (exactly like management bot)
        print(f"\n🔍 Step 4: Authorization")
        admin_id = 7729984017
        auth_success = add_authorized_delivery_personnel(telegram_id, name, admin_id)
        
        if not auth_success:
            print(f"❌ Failed to add to authorized delivery personnel")
            return False
        print(f"✅ Added to authorized delivery personnel")
        
        # Step 5: Verification (exactly like management bot)
        print(f"\n🔍 Step 5: Verification")
        from src.bots.delivery_bot import clear_authorization_cache, get_authorized_delivery_ids_from_firebase, get_personnel_by_telegram_id
        
        # Clear cache and get fresh data
        clear_authorization_cache()
        fresh_authorized_ids = get_authorized_delivery_ids_from_firebase()
        
        print(f"📋 Fresh authorized IDs: {fresh_authorized_ids}")
        print(f"🔍 Checking if {telegram_id} (type: {type(telegram_id)}) is in list...")
        
        # This is the critical check that was failing before the fix
        if telegram_id in fresh_authorized_ids:
            print(f"✅ Authorization verification PASSED")
        else:
            print(f"❌ Authorization verification FAILED")
            print(f"   Telegram ID: {telegram_id} (type: {type(telegram_id)})")
            print(f"   Authorized IDs: {fresh_authorized_ids}")
            print(f"   ID types in list: {[type(x) for x in fresh_authorized_ids[:3]]}")
            return False
        
        # Test personnel lookup
        personnel_lookup = get_personnel_by_telegram_id(telegram_id)
        if personnel_lookup:
            print(f"✅ Personnel lookup successful: {personnel_lookup.name}")
        else:
            print(f"❌ Personnel lookup failed")
            return False
        
        print(f"\n🎉 MANAGEMENT BOT WORKFLOW SIMULATION SUCCESSFUL!")
        print(f"✅ Personnel {name} (ID: {telegram_id}) added successfully")
        print(f"✅ Authorization verification works correctly")
        print(f"✅ Data type consistency maintained")
        
        return True
        
    except Exception as e:
        print(f"❌ Management bot workflow simulation failed: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_order_broadcast_readiness():
    """Test that the system is ready for order broadcasts"""
    print("\n📡 TESTING ORDER BROADCAST READINESS")
    print("=" * 50)
    
    try:
        from src.bots.delivery_bot import get_authorized_delivery_ids_from_firebase
        
        # Get all authorized delivery personnel
        authorized_ids = get_authorized_delivery_ids_from_firebase()
        
        print(f"📋 Total authorized personnel: {len(authorized_ids)}")
        print(f"📊 Authorized IDs: {authorized_ids}")
        
        # Filter out admin IDs to get only delivery personnel
        admin_ids = [7729984017]
        delivery_personnel_ids = [id for id in authorized_ids if id not in admin_ids]
        
        print(f"👥 Delivery personnel count: {len(delivery_personnel_ids)}")
        print(f"👤 Delivery personnel IDs: {delivery_personnel_ids}")
        
        if len(delivery_personnel_ids) >= 3:  # We added 3 personnel total
            print(f"✅ System ready for order broadcasts")
            print(f"✅ {len(delivery_personnel_ids)} delivery personnel available")
            return True
        else:
            print(f"⚠️ Only {len(delivery_personnel_ids)} delivery personnel available")
            return False
        
    except Exception as e:
        print(f"❌ Order broadcast readiness test failed: {e}")
        return False

def generate_system_status_report():
    """Generate a comprehensive system status report"""
    print("\n📊 SYSTEM STATUS REPORT")
    print("=" * 50)
    
    try:
        from src.firebase_db import get_data
        from src.bots.delivery_bot import get_authorized_delivery_ids_from_firebase
        
        # Check all collections
        collections = [
            "delivery_personnel",
            "delivery_personnel_earnings",
            "delivery_personnel_availability", 
            "delivery_personnel_capacity",
            "delivery_personnel_zones",
            "delivery_personnel_performance",
            "authorized_delivery_personnel"
        ]
        
        print(f"📋 Firebase Collections Status:")
        for collection in collections:
            data = get_data(collection) or {}
            print(f"   {collection}: {len(data)} records")
        
        # Authorization system status
        authorized_ids = get_authorized_delivery_ids_from_firebase()
        admin_ids = [7729984017]
        delivery_ids = [id for id in authorized_ids if id not in admin_ids]
        
        print(f"\n🔐 Authorization System Status:")
        print(f"   Total authorized: {len(authorized_ids)}")
        print(f"   Admin IDs: {admin_ids}")
        print(f"   Delivery personnel: {len(delivery_ids)}")
        print(f"   All IDs are integers: {all(isinstance(x, int) for x in authorized_ids)}")
        
        # Personnel details
        personnel_data = get_data("delivery_personnel") or {}
        print(f"\n👥 Personnel Details:")
        for pid, pdata in personnel_data.items():
            name = pdata.get('name', 'Unknown')
            tid = pdata.get('telegram_id', 'N/A')
            status = pdata.get('status', 'unknown')
            print(f"   {name} (ID: {tid}, Status: {status}, Personnel: {pid})")
        
        return True
        
    except Exception as e:
        print(f"❌ System status report failed: {e}")
        return False

def main():
    """Run complete management bot integration verification"""
    print("🚀 MANAGEMENT BOT INTEGRATION VERIFICATION")
    print("=" * 60)
    
    # Step 1: Simulate management bot workflow
    step1_success = simulate_management_bot_workflow()
    
    # Step 2: Test order broadcast readiness
    step2_success = test_order_broadcast_readiness()
    
    # Step 3: Generate system status report
    step3_success = generate_system_status_report()
    
    # Final summary
    print("\n" + "=" * 60)
    print("📊 VERIFICATION SUMMARY")
    print("=" * 60)
    
    steps = [
        ("Management Bot Workflow", step1_success),
        ("Order Broadcast Readiness", step2_success),
        ("System Status Report", step3_success)
    ]
    
    passed = sum(success for _, success in steps)
    total = len(steps)
    
    for step_name, success in steps:
        status = "✅ PASSED" if success else "❌ FAILED"
        print(f"{status}: {step_name}")
    
    print(f"\nOverall: {passed}/{total} verifications passed")
    
    if all(success for _, success in steps):
        print("\n🎉 MANAGEMENT BOT INTEGRATION VERIFIED!")
        print("✅ Authorization verification bug is FIXED")
        print("✅ Data type consistency maintained")
        print("✅ Fresh personnel addition works perfectly")
        print("✅ System ready for production use")
        print("\n📋 READY FOR:")
        print("   • Adding delivery personnel via management bot")
        print("   • Order broadcast notifications")
        print("   • Complete delivery workflow")
        return True
    else:
        print("\n⚠️ VERIFICATION INCOMPLETE")
        print("Some issues detected. Please review above.")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
