#!/usr/bin/env python3
"""
Quick script to check delivery personnel status
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from src.firebase_db import get_data

def main():
    print("=== DELIVERY PERSONNEL STATUS CHECK ===")
    
    # Get data from Firebase
    personnel_data = get_data("delivery_personnel") or {}
    availability_data = get_data("delivery_personnel_availability") or {}
    capacity_data = get_data("delivery_personnel_capacity") or {}
    
    print(f"Found {len(personnel_data)} delivery personnel")
    print()
    
    for personnel_id, data in personnel_data.items():
        print(f"Personnel ID: {personnel_id}")
        print(f"  Name: {data.get('name', 'N/A')}")
        print(f"  Telegram ID: {data.get('telegram_id', 'N/A')}")
        print(f"  Status: {data.get('status', 'N/A')}")
        print(f"  Availability: {availability_data.get(personnel_id, 'N/A')}")
        print(f"  Is Verified: {data.get('is_verified', False)}")
        print(f"  Capacity: {capacity_data.get(personnel_id, 0)}/{data.get('max_capacity', 5)}")
        print(f"  Service Areas: {data.get('service_areas', [])}")
        print("---")

if __name__ == "__main__":
    main()
