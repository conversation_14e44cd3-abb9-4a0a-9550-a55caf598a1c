#!/usr/bin/env python3
"""
Simple verification script for management bot fixes.
Checks that all critical functions are available and working.
"""

import sys
import os

# Add the project root to the Python path
sys.path.insert(0, os.path.abspath('.'))

def check_imports():
    """Check that all required functions can be imported"""
    print("🔍 Checking imports...")
    
    try:
        # Management bot functions
        from src.bots.management_bot import (
            refresh_personnel_data,
            refresh_availability_data, 
            refresh_analytics_data,
            invalidate_personnel_cache,
            validate_firebase_operation,
            safe_firebase_set,
            safe_firebase_update
        )
        print("  ✅ Management bot functions imported successfully")
        
        # Delivery personnel utils
        from src.utils.delivery_personnel_utils import (
            refresh_delivery_personnel_data,
            find_available_personnel_with_capacity_check
        )
        print("  ✅ Delivery personnel utils imported successfully")
        
        # Firebase functions
        from src.firebase_db import get_data, set_data, update_data, delete_data
        print("  ✅ Firebase functions imported successfully")
        
        return True
        
    except ImportError as e:
        print(f"  ❌ Import error: {e}")
        return False
    except Exception as e:
        print(f"  ❌ Unexpected error: {e}")
        return False

def check_function_availability():
    """Check that key functions are callable"""
    print("\n🔧 Checking function availability...")
    
    try:
        from src.bots.management_bot import (
            refresh_personnel_data,
            invalidate_personnel_cache,
            validate_firebase_operation
        )
        
        # Test function callability
        print("  ✅ refresh_personnel_data is callable:", callable(refresh_personnel_data))
        print("  ✅ invalidate_personnel_cache is callable:", callable(invalidate_personnel_cache))
        print("  ✅ validate_firebase_operation is callable:", callable(validate_firebase_operation))
        
        # Test validation function
        valid_result = validate_firebase_operation("set", "test/path", {"test": "data"})
        invalid_result = validate_firebase_operation("set", "../invalid/path", {"test": "data"})
        
        print(f"  ✅ Validation works - Valid path: {valid_result}, Invalid path: {not invalid_result}")
        
        return True
        
    except Exception as e:
        print(f"  ❌ Error checking functions: {e}")
        return False

def check_data_models():
    """Check that data models are accessible"""
    print("\n📊 Checking data models...")
    
    try:
        from src.data_models import (
            delivery_personnel,
            delivery_personnel_availability,
            delivery_personnel_capacity
        )
        
        print(f"  ✅ delivery_personnel model: {type(delivery_personnel)} with {len(delivery_personnel)} entries")
        print(f"  ✅ delivery_personnel_availability model: {type(delivery_personnel_availability)} with {len(delivery_personnel_availability)} entries")
        print(f"  ✅ delivery_personnel_capacity model: {type(delivery_personnel_capacity)} with {len(delivery_personnel_capacity)} entries")
        
        return True
        
    except Exception as e:
        print(f"  ❌ Error checking data models: {e}")
        return False

def check_firebase_connectivity():
    """Check Firebase connectivity"""
    print("\n🔥 Checking Firebase connectivity...")
    
    try:
        from src.firebase_db import get_data
        
        # Try to read system health data
        health_data = get_data("system_health")
        print(f"  ✅ Firebase read test successful: {health_data is not None}")
        
        # Try to read personnel data
        personnel_data = get_data("delivery_personnel")
        if personnel_data:
            print(f"  ✅ Personnel data accessible: {len(personnel_data)} records")
        else:
            print("  ℹ️  No personnel data found (this is normal for new systems)")
        
        return True
        
    except Exception as e:
        print(f"  ❌ Firebase connectivity error: {e}")
        return False

def test_real_time_functions():
    """Test real-time data refresh functions"""
    print("\n⚡ Testing real-time functions...")
    
    try:
        from src.bots.management_bot import (
            refresh_personnel_data,
            refresh_availability_data,
            refresh_analytics_data
        )
        
        # Test personnel data refresh
        personnel_data = refresh_personnel_data()
        print(f"  ✅ Personnel data refresh: {len(personnel_data)} records")
        
        # Test availability data refresh  
        availability_data = refresh_availability_data()
        print(f"  ✅ Availability data refresh: {len(availability_data)} records")
        
        # Test analytics data refresh
        analytics_data = refresh_analytics_data()
        total_records = sum(len(v) if isinstance(v, dict) else 0 for v in analytics_data.values())
        print(f"  ✅ Analytics data refresh: {total_records} total records")
        
        return True
        
    except Exception as e:
        print(f"  ❌ Error testing real-time functions: {e}")
        return False

def main():
    """Run all verification checks"""
    print("🚀 Management Bot Fixes Verification")
    print("=" * 50)
    
    checks = [
        check_imports,
        check_function_availability,
        check_data_models,
        check_firebase_connectivity,
        test_real_time_functions
    ]
    
    passed = 0
    total = len(checks)
    
    for check in checks:
        try:
            if check():
                passed += 1
            else:
                print("  ⚠️  Check failed")
        except Exception as e:
            print(f"  ❌ Check error: {e}")
    
    print("\n" + "=" * 50)
    print(f"📊 Verification Results: {passed}/{total} checks passed")
    
    if passed == total:
        print("🎉 All verification checks passed!")
        print("✅ Management bot fixes are properly implemented")
        return True
    else:
        print("⚠️  Some checks failed - review issues above")
        return False

if __name__ == "__main__":
    success = main()
    print(f"\n🏁 Verification {'PASSED' if success else 'FAILED'}")
