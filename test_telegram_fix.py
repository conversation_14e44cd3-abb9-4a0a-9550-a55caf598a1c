#!/usr/bin/env python3
"""
Test script to verify the Telegram API error fixes for decimal numbers in MarkdownV2
"""

import sys
import os

# Add the src directory to the Python path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

def test_escape_markdown_function():
    """Test that the escape_markdown function properly escapes decimal numbers"""
    try:
        from bots.management_bot import escape_markdown
        
        print("🔧 Testing escape_markdown function...")
        print("=" * 60)
        
        test_cases = [
            ("25.50", "25\\.50"),
            ("125.75", "125\\.75"),
            ("0.00", "0\\.00"),
            ("1234.56", "1234\\.56"),
            ("+251.912.345.678", "\\+251\\.912\\.345\\.678"),
            ("Daily: 25.50 birr", "Daily: 25\\.50 birr"),
            ("Weekly: 125.75 birr", "Weekly: 125\\.75 birr"),
            ("Success Rate: 95.5%", "Success Rate: 95\\.5%"),
            ("Average: 12.34", "Average: 12\\.34")
        ]
        
        all_passed = True
        for original, expected in test_cases:
            result = escape_markdown(original)
            if result == expected:
                print(f"✅ '{original}' -> '{result}'")
            else:
                print(f"❌ '{original}' -> '{result}' (expected: '{expected}')")
                all_passed = False
        
        return all_passed
        
    except ImportError as e:
        print(f"❌ Could not import escape_markdown: {e}")
        return False

def test_decimal_formatting():
    """Test that decimal number formatting is properly escaped"""
    try:
        from bots.management_bot import escape_markdown
        
        print("\n💰 Testing decimal number formatting...")
        print("=" * 60)
        
        # Test the exact scenario that was causing the error
        daily_earnings = 25.50
        weekly_earnings = 125.75
        
        # Old problematic way (would cause error)
        old_format = f"Daily: {daily_earnings:.2f} birr - Weekly: {weekly_earnings:.2f} birr"
        print(f"❌ Old format (problematic): {old_format}")
        
        # Check for unescaped periods
        unescaped_periods = sum(1 for i, char in enumerate(old_format) 
                               if char == '.' and (i == 0 or old_format[i-1] != '\\'))
        print(f"   Unescaped periods: {unescaped_periods}")
        
        # New fixed way
        daily_str = escape_markdown(f"{daily_earnings:.2f}")
        weekly_str = escape_markdown(f"{weekly_earnings:.2f}")
        new_format = f"Daily: {daily_str} birr - Weekly: {weekly_str} birr"
        print(f"✅ New format (fixed): {new_format}")
        
        # Check for unescaped periods in new format
        unescaped_periods_new = sum(1 for i, char in enumerate(new_format) 
                                   if char == '.' and (i == 0 or new_format[i-1] != '\\'))
        print(f"   Unescaped periods: {unescaped_periods_new}")
        
        return unescaped_periods_new == 0
        
    except ImportError as e:
        print(f"❌ Could not import escape_markdown: {e}")
        return False

def test_personnel_list_format():
    """Test the exact personnel list format that was causing errors"""
    try:
        from bots.management_bot import escape_markdown
        
        print("\n👥 Testing personnel list formatting...")
        print("=" * 60)
        
        # Simulate personnel data that would cause the error
        personnel_data = [
            {
                "name": "John Doe",
                "phone": "+251.912.345.678",
                "daily_earnings": 25.50,
                "weekly_earnings": 125.75
            },
            {
                "name": "Jane Smith",
                "phone": "+251-987-654-321",
                "daily_earnings": 30.25,
                "weekly_earnings": 150.00
            }
        ]
        
        print("Testing personnel list generation...")
        
        for index, person in enumerate(personnel_data, 1):
            name = escape_markdown(person["name"])
            phone = escape_markdown(person["phone"])
            
            # Test the fixed format
            daily_str = escape_markdown(f"{person['daily_earnings']:.2f}")
            weekly_str = escape_markdown(f"{person['weekly_earnings']:.2f}")
            
            line = f"{index}\\. {name} \\- {phone} \\- Daily: {daily_str} birr \\- Weekly: {weekly_str} birr"
            
            # Check for unescaped periods
            unescaped_periods = sum(1 for i, char in enumerate(line) 
                                   if char == '.' and (i == 0 or line[i-1] != '\\'))
            
            if unescaped_periods == 0:
                print(f"✅ Person {index}: No unescaped periods")
                print(f"   {line}")
            else:
                print(f"❌ Person {index}: {unescaped_periods} unescaped periods")
                print(f"   {line}")
                return False
        
        return True
        
    except ImportError as e:
        print(f"❌ Could not import escape_markdown: {e}")
        return False

def test_analytics_format():
    """Test analytics formatting with decimal numbers"""
    try:
        from bots.management_bot import escape_markdown
        
        print("\n📊 Testing analytics formatting...")
        print("=" * 60)
        
        # Test analytics data
        total_revenue = 1234.56
        avg_order = 45.75
        success_rate = 95.5
        
        # Test the fixed format
        total_revenue_str = escape_markdown(f"{total_revenue:.2f}")
        avg_order_str = escape_markdown(f"{avg_order:.2f}")
        success_rate_str = escape_markdown(f"{success_rate:.1f}")
        
        analytics_text = f"""
**Revenue:**
• Total Revenue: {total_revenue_str} birr
• Average Order: {avg_order_str} birr
• Success Rate: {success_rate_str}%
        """
        
        # Check for unescaped periods
        unescaped_periods = sum(1 for i, char in enumerate(analytics_text) 
                               if char == '.' and (i == 0 or analytics_text[i-1] != '\\'))
        
        if unescaped_periods == 0:
            print("✅ Analytics format: No unescaped periods")
            print(f"Sample text: {analytics_text.strip()}")
            return True
        else:
            print(f"❌ Analytics format: {unescaped_periods} unescaped periods")
            print(f"Sample text: {analytics_text.strip()}")
            return False
        
    except ImportError as e:
        print(f"❌ Could not import escape_markdown: {e}")
        return False

def main():
    """Run all tests"""
    print("🚨 TELEGRAM API ERROR FIX VERIFICATION")
    print("Testing fixes for: 'can't parse entities: Character '.' is reserved and must be escaped'")
    print("=" * 80)
    
    # Run all tests
    test_results = []
    test_results.append(("Escape Function", test_escape_markdown_function()))
    test_results.append(("Decimal Formatting", test_decimal_formatting()))
    test_results.append(("Personnel List", test_personnel_list_format()))
    test_results.append(("Analytics Format", test_analytics_format()))
    
    # Summary
    print("\n" + "=" * 80)
    print("📋 TEST RESULTS SUMMARY:")
    
    all_passed = True
    for test_name, passed in test_results:
        status = "✅ PASSED" if passed else "❌ FAILED"
        print(f"  {test_name}: {status}")
        if not passed:
            all_passed = False
    
    print("\n" + "=" * 80)
    if all_passed:
        print("🎉 ALL TESTS PASSED!")
        print("\n✅ The Telegram API error should now be fixed!")
        print("\n📋 FIXES APPLIED:")
        print("• Decimal numbers in earnings are now properly escaped")
        print("• Personnel list formatting uses escaped decimal values")
        print("• Analytics displays use escaped decimal values")
        print("• All special characters including periods are escaped")
        print("\n🔧 NEXT STEPS:")
        print("1. Test the management bot Edit and Delete buttons")
        print("2. Verify personnel management works without API errors")
        print("3. Check that all decimal displays are properly formatted")
    else:
        print("⚠️ SOME TESTS FAILED!")
        print("Please review the failed tests and fix any remaining issues.")

if __name__ == "__main__":
    main()
