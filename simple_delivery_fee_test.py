#!/usr/bin/env python3
"""
Simple test for delivery fee calculation fixes
"""

import sys
import os

# Add the src directory to the Python path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

def test_basic_functionality():
    """Test basic delivery fee functionality"""
    print("🧪 Testing basic delivery fee functionality...")

    try:
        from src.data_storage import get_delivery_fee, get_all_areas, get_all_delivery_locations, load_user_data

        print("✅ Successfully imported required modules")

        # Load data from Firebase first
        print("📥 Loading data from Firebase...")
        load_user_data()
        print("✅ Data loaded from Firebase")

        # Test data retrieval
        areas = get_all_areas()
        locations = get_all_delivery_locations()
        
        print(f"📊 Found {len(areas)} areas and {len(locations)} locations")
        
        if areas and locations:
            # Test with first area and location
            test_area = areas[0]
            test_location = locations[0]
            area_id = test_area["id"]
            location_id = test_location["id"]
            
            print(f"🔍 Testing with area '{test_area['name']}' (ID: {area_id}) and location '{test_location['name']}' (ID: {location_id})")
            
            # Test delivery fee retrieval
            fee = get_delivery_fee(area_id, location_id)
            print(f"💰 Retrieved delivery fee: {fee} birr")
            
            if isinstance(fee, (int, float)) and fee >= 0:
                print("✅ Delivery fee retrieval test PASSED")
                return True
            else:
                print(f"❌ Invalid delivery fee: {fee}")
                return False
        else:
            print("❌ No test data available")
            return False
            
    except Exception as e:
        print(f"❌ Error in test: {e}")
        import traceback
        traceback.print_exc()
        return False

def test_order_simulation():
    """Simulate order creation with delivery fee"""
    print("\n🧪 Testing order simulation with delivery fee...")

    try:
        from src.data_storage import get_delivery_fee, get_all_areas, get_all_delivery_locations, load_user_data

        # Load data from Firebase first
        print("📥 Loading data from Firebase...")
        load_user_data()

        areas = get_all_areas()
        locations = get_all_delivery_locations()
        
        if not areas or not locations:
            print("❌ No test data available for simulation")
            return False
        
        # Simulate order creation
        test_area = areas[0]
        test_location = locations[0]
        area_id = test_area["id"]
        location_id = test_location["id"]
        
        # Simulate the fixed handle_delivery_gate logic
        delivery_fee = get_delivery_fee(area_id, location_id)
        
        # Create mock order
        mock_order = {
            "restaurant_area_id": area_id,
            "delivery_gate": test_location["name"],
            "delivery_location_id": location_id,
            "delivery_fee": delivery_fee,
            "items": [{"name": "Test Item", "price": 100}]
        }
        
        # Simulate order summary calculation
        subtotal = sum(item["price"] for item in mock_order["items"])
        order_delivery_fee = mock_order.get("delivery_fee", 0)
        total_price = subtotal + order_delivery_fee
        
        print(f"📋 Order Summary:")
        print(f"   - Subtotal: {subtotal} birr")
        print(f"   - Delivery Fee: {order_delivery_fee} birr")
        print(f"   - Total: {total_price} birr")
        
        if order_delivery_fee >= 0 and total_price > subtotal:
            print("✅ Order simulation test PASSED")
            return True
        else:
            print(f"❌ Order simulation failed - invalid calculations")
            return False
            
    except Exception as e:
        print(f"❌ Error in order simulation: {e}")
        import traceback
        traceback.print_exc()
        return False

def main():
    """Run simple delivery fee tests"""
    print("🚀 Starting simple delivery fee tests...")
    print("=" * 50)
    
    tests = [
        test_basic_functionality,
        test_order_simulation
    ]
    
    passed = 0
    total = len(tests)
    
    for test_func in tests:
        try:
            if test_func():
                passed += 1
        except Exception as e:
            print(f"❌ Test failed with exception: {e}")
    
    print("\n" + "=" * 50)
    print("TEST SUMMARY")
    print("=" * 50)
    print(f"Passed: {passed}/{total}")
    
    if passed == total:
        print("🎉 All tests passed!")
        return True
    else:
        print(f"💥 {total - passed} test(s) failed!")
        return False

if __name__ == "__main__":
    success = main()
    print(f"\nTest result: {'SUCCESS' if success else 'FAILURE'}")
