#!/usr/bin/env python3
"""
Simple diagnostic test for System Management menu.
"""

import sys
import os

# Add the project root to the Python path
sys.path.insert(0, os.path.abspath('.'))

print("🔍 SYSTEM MANAGEMENT MENU DIAGNOSTIC")
print("=" * 50)

try:
    print("1. Testing basic imports...")
    from src.bots.management_bot import create_main_menu_keyboard
    print("✅ create_main_menu_keyboard imported")
    
    keyboard = create_main_menu_keyboard()
    print("✅ Main menu keyboard created")
    
    print("\n2. Checking menu buttons...")
    for i, row in enumerate(keyboard.keyboard):
        for j, button in enumerate(row):
            print(f"   Button [{i},{j}]: '{button.text}' -> '{button.callback_data}'")
            if "System Management" in button.text:
                print("   ✅ FOUND System Management button!")
    
    print("\n3. Testing System Management functions...")
    from src.bots.management_bot import show_system_management_menu
    print("✅ show_system_management_menu imported")
    
    from src.bots.management_bot import is_authorized_for_reset
    print("✅ is_authorized_for_reset imported")
    
    # Test authorization
    auth_result = is_authorized_for_reset("7729984017")
    print(f"✅ Authorization test: {auth_result}")
    
    print("\n4. Testing callback handlers...")
    from src.bots.management_bot import handle_callback_query
    print("✅ handle_callback_query imported")
    
    print("\n🎉 ALL BASIC TESTS PASSED!")
    print("\nThe System Management menu should be working.")
    print("If you can't see it, try:")
    print("1. Start management bot: python main.py --bot management")
    print("2. Send /start to the bot")
    print("3. Look for '🔧 System Management' button")
    
except Exception as e:
    print(f"❌ Error: {e}")
    import traceback
    traceback.print_exc()
