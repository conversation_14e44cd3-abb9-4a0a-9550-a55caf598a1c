#!/usr/bin/env python3
"""
Test script to simulate the exact button click scenario that was causing the Telegram API error
"""

import sys
import os
from unittest.mock import Mock, <PERSON><PERSON><PERSON>

# Add the src directory to the Python path
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'src'))

def test_edit_button_callback():
    """Test the Edit Personnel button callback that was causing the error"""
    try:
        from bots.management_bot import start_edit_personnel_selection, escape_markdown
        
        print("🔧 Testing Edit Personnel button callback...")
        print("=" * 60)
        
        # Mock the callback query object
        mock_call = Mock()
        mock_call.id = "test_callback_123"
        mock_call.message = Mock()
        mock_call.message.chat = Mock()
        mock_call.message.chat.id = 123456789
        mock_call.message.message_id = 987654321
        mock_call.data = "pers_edit_select"
        
        # Mock the management bot
        import bots.management_bot as mgmt_bot
        original_answer_callback = mgmt_bot.management_bot.answer_callback_query
        original_edit_message = mgmt_bot.management_bot.edit_message_text
        original_get_data = mgmt_bot.get_data
        original_get_earnings = mgmt_bot.get_all_personnel_earnings
        
        # Track calls
        callback_answers = []
        message_edits = []
        
        def mock_answer_callback(callback_id, text=None, show_alert=False):
            callback_answers.append({"id": callback_id, "text": text, "alert": show_alert})
            print(f"📞 Callback answered: {text}")
            return True
        
        def mock_edit_message(text, chat_id, message_id, reply_markup=None, parse_mode=None):
            message_edits.append({
                "text": text,
                "chat_id": chat_id,
                "message_id": message_id,
                "parse_mode": parse_mode
            })
            print(f"✏️ Message edited with parse_mode: {parse_mode}")
            
            # Check for unescaped periods in MarkdownV2
            if parse_mode == 'MarkdownV2':
                unescaped_periods = sum(1 for i, char in enumerate(text) 
                                       if char == '.' and (i == 0 or text[i-1] != '\\'))
                if unescaped_periods > 0:
                    print(f"❌ Found {unescaped_periods} unescaped periods in message!")
                    return False
                else:
                    print(f"✅ All periods properly escaped in MarkdownV2 message")
            
            return True
        
        def mock_get_data(path):
            # Return mock personnel data with decimal earnings
            if path == "delivery_personnel":
                return {
                    "person1": {
                        "name": "John Doe",
                        "phone_number": "+251.912.345.678",
                        "telegram_id": "123456789"
                    },
                    "person2": {
                        "name": "Jane Smith", 
                        "phone_number": "+251-987-654-321",
                        "telegram_id": "987654321"
                    }
                }
            return {}
        
        def mock_get_earnings():
            # Return mock earnings data with decimal values
            return {
                "person1": {
                    "daily_earnings": 25.50,
                    "weekly_earnings": 125.75
                },
                "person2": {
                    "daily_earnings": 30.25,
                    "weekly_earnings": 150.00
                }
            }
        
        # Apply mocks
        mgmt_bot.management_bot.answer_callback_query = mock_answer_callback
        mgmt_bot.management_bot.edit_message_text = mock_edit_message
        mgmt_bot.get_data = mock_get_data
        mgmt_bot.get_all_personnel_earnings = mock_get_earnings
        
        # Test the function that was causing the error
        try:
            start_edit_personnel_selection(mock_call)
            
            # Check results
            if callback_answers:
                print(f"✅ Callback query answered successfully")
            
            if message_edits:
                edit = message_edits[0]
                if edit["parse_mode"] == "MarkdownV2":
                    print(f"✅ Message edited with MarkdownV2 successfully")
                    return True
                else:
                    print(f"❌ Unexpected parse mode: {edit['parse_mode']}")
                    return False
            else:
                print(f"❌ No message edits performed")
                return False
                
        except Exception as e:
            print(f"❌ Error during function execution: {e}")
            return False
        
        finally:
            # Restore original functions
            mgmt_bot.management_bot.answer_callback_query = original_answer_callback
            mgmt_bot.management_bot.edit_message_text = original_edit_message
            mgmt_bot.get_data = original_get_data
            mgmt_bot.get_all_personnel_earnings = original_get_earnings
        
    except ImportError as e:
        print(f"❌ Could not import required functions: {e}")
        return False

def test_delete_button_callback():
    """Test the Delete Personnel button callback that was causing the error"""
    try:
        from bots.management_bot import start_delete_personnel_selection
        
        print("\n🗑️ Testing Delete Personnel button callback...")
        print("=" * 60)
        
        # Mock the callback query object
        mock_call = Mock()
        mock_call.id = "test_callback_456"
        mock_call.message = Mock()
        mock_call.message.chat = Mock()
        mock_call.message.chat.id = 123456789
        mock_call.message.message_id = 987654321
        mock_call.data = "pers_delete_select"
        
        # Mock the management bot
        import bots.management_bot as mgmt_bot
        original_answer_callback = mgmt_bot.management_bot.answer_callback_query
        original_edit_message = mgmt_bot.management_bot.edit_message_text
        original_get_data = mgmt_bot.get_data
        original_get_earnings = mgmt_bot.get_all_personnel_earnings
        
        # Track calls
        callback_answers = []
        message_edits = []
        
        def mock_answer_callback(callback_id, text=None, show_alert=False):
            callback_answers.append({"id": callback_id, "text": text, "alert": show_alert})
            print(f"📞 Callback answered: {text}")
            return True
        
        def mock_edit_message(text, chat_id, message_id, reply_markup=None, parse_mode=None):
            message_edits.append({
                "text": text,
                "chat_id": chat_id,
                "message_id": message_id,
                "parse_mode": parse_mode
            })
            print(f"✏️ Message edited with parse_mode: {parse_mode}")
            
            # Check for unescaped periods in MarkdownV2
            if parse_mode == 'MarkdownV2':
                unescaped_periods = sum(1 for i, char in enumerate(text) 
                                       if char == '.' and (i == 0 or text[i-1] != '\\'))
                if unescaped_periods > 0:
                    print(f"❌ Found {unescaped_periods} unescaped periods in message!")
                    return False
                else:
                    print(f"✅ All periods properly escaped in MarkdownV2 message")
            
            return True
        
        def mock_get_data(path):
            # Return mock personnel data
            if path == "delivery_personnel":
                return {
                    "person1": {
                        "name": "John Doe",
                        "phone_number": "+251.912.345.678",
                        "telegram_id": "123456789"
                    }
                }
            return {}
        
        def mock_get_earnings():
            # Return mock earnings data with decimal values
            return {
                "person1": {
                    "daily_earnings": 25.50,
                    "weekly_earnings": 125.75
                }
            }
        
        # Apply mocks
        mgmt_bot.management_bot.answer_callback_query = mock_answer_callback
        mgmt_bot.management_bot.edit_message_text = mock_edit_message
        mgmt_bot.get_data = mock_get_data
        mgmt_bot.get_all_personnel_earnings = mock_get_earnings
        
        # Test the function
        try:
            start_delete_personnel_selection(mock_call)
            
            # Check results
            if callback_answers and message_edits:
                print(f"✅ Delete button callback executed successfully")
                return True
            else:
                print(f"❌ Delete button callback failed")
                return False
                
        except Exception as e:
            print(f"❌ Error during delete function execution: {e}")
            return False
        
        finally:
            # Restore original functions
            mgmt_bot.management_bot.answer_callback_query = original_answer_callback
            mgmt_bot.management_bot.edit_message_text = original_edit_message
            mgmt_bot.get_data = original_get_data
            mgmt_bot.get_all_personnel_earnings = original_get_earnings
        
    except ImportError as e:
        print(f"❌ Could not import required functions: {e}")
        return False

def main():
    """Run button callback tests"""
    print("🚨 BUTTON CALLBACK ERROR FIX TEST")
    print("Testing the exact Edit/Delete button scenarios that were causing API errors")
    print("=" * 80)
    
    # Run tests
    edit_success = test_edit_button_callback()
    delete_success = test_delete_button_callback()
    
    # Summary
    print("\n" + "=" * 80)
    print("📋 BUTTON TEST RESULTS:")
    print(f"  Edit Button: {'✅ PASSED' if edit_success else '❌ FAILED'}")
    print(f"  Delete Button: {'✅ PASSED' if delete_success else '❌ FAILED'}")
    
    if edit_success and delete_success:
        print("\n🎉 ALL BUTTON TESTS PASSED!")
        print("\n✅ The Edit and Delete buttons should now work without Telegram API errors!")
        print("\n📋 VERIFICATION:")
        print("• Edit Personnel button properly escapes decimal numbers")
        print("• Delete Personnel button properly escapes decimal numbers") 
        print("• MarkdownV2 formatting is safe for Telegram API")
        print("• No unescaped periods in personnel earnings display")
    else:
        print("\n⚠️ SOME BUTTON TESTS FAILED!")
        print("Please review the failed tests and ensure all decimal numbers are properly escaped.")

if __name__ == "__main__":
    main()
