#!/usr/bin/env python3
"""
Complete Test of Management Bot Callback Fixes
Verifies that all inline keyboard buttons work properly without loading indicators
"""

import sys
import os
import time

# Add the project root to the Python path
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

def test_callback_fixes():
    """Test that all callback fixes are working properly"""
    print("🔧 TESTING MANAGEMENT BOT CALLBACK FIXES")
    print("=" * 60)
    
    try:
        # Import required modules
        from src.bots.management_bot import management_bot, handle_callback_query
        from telebot.types import CallbackQuery, User, Chat, Message
        
        print("✅ Management bot modules imported successfully")
        
        # Test bot connection
        bot_info = management_bot.get_me()
        print(f"✅ Bot connected: @{bot_info.username}")
        
        # Create mock authorized user
        mock_user = User(
            id=7729984017,
            is_bot=False,
            first_name="Test",
            username="testuser",
            last_name="User",
            language_code="en"
        )
        
        # Create mock chat
        mock_chat = Chat(
            id=7729984017,
            type="private"
        )
        
        # Create mock message
        mock_message = Message(
            message_id=1,
            from_user=mock_user,
            date=int(time.time()),
            chat=mock_chat,
            content_type="text",
            options={},
            json_string=""
        )
        
        # Track callback answers and responses
        callback_answers = []
        message_responses = []
        
        # Mock bot methods
        original_answer_callback_query = management_bot.answer_callback_query
        original_edit_message_text = management_bot.edit_message_text
        
        def mock_answer_callback_query(callback_query_id, text=None, show_alert=False, **kwargs):
            callback_answers.append({
                'id': callback_query_id,
                'text': text,
                'show_alert': show_alert
            })
            return True
        
        def mock_edit_message_text(text, chat_id, message_id, **kwargs):
            message_responses.append({
                'text': text,
                'chat_id': chat_id,
                'message_id': message_id,
                'has_keyboard': 'reply_markup' in kwargs
            })
            return type('MockMessage', (), {'message_id': message_id})()
        
        # Replace bot methods
        management_bot.answer_callback_query = mock_answer_callback_query
        management_bot.edit_message_text = mock_edit_message_text
        
        # Test scenarios
        test_scenarios = [
            {
                'name': '👥 Personnel Management',
                'callback_data': 'mgmt_personnel',
                'expected_callback_text': '👥 Loading personnel management...',
                'should_edit_message': True,
                'expected_content': 'Personnel Management'
            },
            {
                'name': '📊 Analytics Dashboard',
                'callback_data': 'mgmt_analytics',
                'expected_callback_text': '📊 Loading analytics dashboard...',
                'should_edit_message': True,
                'expected_content': 'Analytics Dashboard'
            },
            {
                'name': '🏢 Main Menu',
                'callback_data': 'mgmt_main',
                'expected_callback_text': '🏢 Loading main menu...',
                'should_edit_message': True,
                'expected_content': 'Wiz Aroma Management Bot'
            },
            {
                'name': '📈 Reports (Placeholder)',
                'callback_data': 'mgmt_reports',
                'expected_callback_text': '📈 Reports feature coming soon!',
                'should_edit_message': False,
                'expected_content': None
            },
            {
                'name': '💰 Earnings (Placeholder)',
                'callback_data': 'mgmt_earnings',
                'expected_callback_text': '💰 Earnings feature coming soon!',
                'should_edit_message': False,
                'expected_content': None
            },
            {
                'name': '🔄 Refresh Data (Placeholder)',
                'callback_data': 'mgmt_refresh',
                'expected_callback_text': '🔄 Refreshing data...',
                'should_edit_message': False,
                'expected_content': None
            },
            {
                'name': 'ℹ️ System Info (Placeholder)',
                'callback_data': 'mgmt_info',
                'expected_callback_text': 'ℹ️ System info feature coming soon!',
                'should_edit_message': False,
                'expected_content': None
            }
        ]
        
        print(f"\n🧪 Testing {len(test_scenarios)} callback scenarios...")
        
        all_passed = True
        
        for scenario in test_scenarios:
            print(f"\n--- Testing: {scenario['name']} ---")
            
            # Clear previous responses
            callback_answers.clear()
            message_responses.clear()
            
            # Create mock callback query
            mock_callback = CallbackQuery(
                id=f"test_{scenario['callback_data']}",
                from_user=mock_user,
                message=mock_message,
                data=scenario['callback_data'],
                chat_instance="test_chat",
                json_string=""
            )
            
            try:
                # Test the callback handler
                handle_callback_query(mock_callback)
                
                # Verify callback was answered
                if callback_answers:
                    answer = callback_answers[0]
                    if answer['text'] == scenario['expected_callback_text']:
                        print(f"✅ Callback answered correctly: {answer['text']}")
                    else:
                        print(f"❌ Wrong callback text. Expected: {scenario['expected_callback_text']}, Got: {answer['text']}")
                        all_passed = False
                else:
                    print(f"❌ No callback answer received")
                    all_passed = False
                
                # Verify message editing (if expected)
                if scenario['should_edit_message']:
                    if message_responses:
                        response = message_responses[0]
                        if scenario['expected_content'] in response['text']:
                            print(f"✅ Message edited correctly with expected content")
                            if response['has_keyboard']:
                                print(f"✅ Inline keyboard included")
                            else:
                                print(f"⚠️ No inline keyboard found")
                        else:
                            print(f"❌ Message content doesn't match. Expected: {scenario['expected_content']}")
                            all_passed = False
                    else:
                        print(f"❌ Expected message edit but none received")
                        all_passed = False
                else:
                    if message_responses:
                        print(f"⚠️ Unexpected message edit for placeholder function")
                    else:
                        print(f"✅ No message edit (as expected for placeholder)")
                        
            except Exception as e:
                print(f"❌ Error testing {scenario['name']}: {e}")
                all_passed = False
        
        # Restore original methods
        management_bot.answer_callback_query = original_answer_callback_query
        management_bot.edit_message_text = original_edit_message_text
        
        print("\n" + "=" * 60)
        if all_passed:
            print("🎉 ALL CALLBACK FIXES WORKING CORRECTLY!")
            print("✅ Loading indicators will be dismissed properly")
            print("✅ All buttons provide appropriate feedback")
            print("✅ Authorization is working correctly")
        else:
            print("❌ SOME CALLBACK FIXES NEED ATTENTION")
        print("=" * 60)
        
        return all_passed
        
    except Exception as e:
        print(f"❌ Error: {e}")
        import traceback
        traceback.print_exc()
        return False

if __name__ == "__main__":
    success = test_callback_fixes()
    if success:
        print("\n🎉 All callback fixes verified!")
    else:
        print("\n💥 Some callback fixes failed!")
        sys.exit(1)
